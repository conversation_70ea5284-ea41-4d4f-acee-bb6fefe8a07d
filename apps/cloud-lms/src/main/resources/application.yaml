spring:
  mail:
    default-encoding: UTF-8
    protocol: smtp
    host: email-smtp.ap-south-1.amazonaws.com
    port: 587
    username: ********************
    password: BHC+HlnuRCE28wD7j2UiJFLpybntOY/QWKvy02Cp/g3f
    from-email: <EMAIL>
  jpa:
    hibernate:
      ddl-auto: update
  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.xml
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  servlet:
    multipart:
      max-file-size: 25MB
      max-request-size: 25MB
  application:
    name: retail-service

app:
  easeBuzzInitiateUrl: https://pay.easebuzz.in/payment/initiateLink
  publicUrl: https://console.wexledu.com/api/public/orgs/
  storageConfig:
    accessKey: 6696MH3R56KJT9DB4RC4
    secretKey: yDNqZoTXGzKBORAFgjGT5pbK2PbFul1nQc288QRF
    endpoint: https://s3.ap-southeast-1.wasabisys.com
    region: ap-southeast-1
  publishTestNotification: false
  globalProfileMigrationUser: blah
  omrPath: wexl-omr
  parseable:
    enabled: false
    #streamName: prodretail -> This is env specific
    baseUrl: http://parseable.omr.svc.cluster.local:8000
    username: admin
    password: password@123
  contentToken: "Bearer eyJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YPzmd87jrmPThQko_Ua-zzEsCOIantN961cPgrPVGgc"
  localeFilesLocation: file:///i18n/
  omr-url: http://omr-service.omr.svc.cluster.local:9081/
  publicDomainUrl: dev.wexl.in
  qrCode:
    url: https://console.wexledu.com/#/qr-signup?uuid=%s
  i18n:
    enJson: en.json
  pmp:
    enabled: false
  dps:
    classTeacherPreference:
      - sectionName: 12A
        subjectSlugs: mathematics,english,evs,social,science,physics,Chemistry,biology
        boardSlug: cbse
  curriculum:
    eduBoards: st-marys-elp,pathway-elp,vidyanjali-elp,schand-elp,ol-elp, obs-gm-elp,bur-elp,ilp-elp,rsgrle-elp,head-word-elp,rsgrrd-elp,blue-bells,nios,nm-tnsdc-elp,cbse-lean-hyphen,pearson-elp,iit-jee-neet-foundation,iit-neet-foundation,stdb,bet,cbse,icse,bset,bj,cbse-elp,ratna-sagar-elp,als-elp,xseed-elp,evershine-elp,intellica-elp,ckds-elp,elp-refer,elp,cbse-ncert-elp,pearson-elp,eupheus-elp,wonder-kid-elp,bset-elp,bset-new-elp
  switchOffExamRevision: true
  validImageExtension: png,jpg,jpeg
  switchOffGroupTest: true
  switchOffGroupMlp: true
  scorm:

    playerLaunchUrl: https://courses.wexledu.com
  security:
    secret: 25f31564122a4e97
  activity:
    events: PRACTICE_COMPLETED, TEST_COMPLETED, ASSESSMENT_COMPLETED, SCHEDULED_TEST_COMPLETED, SCHOOL_TEST_COMPLETED, CORRECTION_COMPLETED, ASSIGNMENT_COMPLETED, REVISION_COMPLETED, COURSE_ASSIGNMENT_COMPLETED, COURSE_TEST_COMPLETED,WORKSHEET_COMPLETED,MOCK_TEST_COMPLETED
  excludeOrgsForMlpAttendance: ach989608,act555,after-my-school,aks040,akshaya,amr977,anh802812,apple,ava818164,bad794,bas050, bas714,bha160192, bha263653,bha288990,bha730324,bha928576,bha999313,blu125,bse089,chanakya,chi141056,cpr268798,dro047408,dwsc, dwss,eksj,excellencia,exp829,fut400337,gac011886,gec,git864,gol117759,gow395,gpu387,ind252,ind547,info,inn146,ins208139,ist358,kak013165,kak116005,kak118073,kak209864,kak304488,kak305058,kak371958,kak499721,kak525229,kak532,kak602192,kak647655,kak690086,kak755936,kak775684,kak840064,kak866498,kak949307,kakatiya-nzmbd,kids,kir311,kit156449,kri053,kri082, kri109,kri168,kri197,kri363,kri370,kri445,kri506,kri557,kri652,kri663,kri786,kri796,kri841,kri856,kri870,kri925,krs114151,krs460202,krs754806,krs849588,kxt224,lea828363,lit761063,lot716,lotus,lotuslap,masterminds,mems,mlp564,nal068848,nal102147,nal238039,nal389146,nal548894,nal622890,nal785964,nal884925,nal919523,nal942970,new133,nil959630,oak355,oak499,obu242,orb319,oswaal,oxford,pcsc,pep054403,pep197034,pla948848,ppk546158,pptp,pro922891,pud263898,pudami,pul715753,radiant-tapovan,rai395,rai503,rav324353,rot797,rvm,sad250788,sai670,saie,sbs253077,sgic,siddhartha,sindhu,sri205,sri927,sshs,stf104,stm809476,tap256750,tes460,the749,tho193213,tho937044,tra610,uned,unicent,ush641943,van650316,ver613831,vid877736,vivekananda,wex658,wex908,wexl-academy,wexl-gitanjali,wexl-internal,wip697586,you882175
  msg91:
    templateId: 63ae9112d6fc0537dd1c01b3
    authkey:  386992AIQn3Fech63a01d0aP1
    sendOtp:   https://api.msg91.com/api/v5/otp?template_id=%s&mobile=%s&authkey=%s
    verifyOtp:  https://api.msg91.com/api/v5/otp/verify?otp=%s&authkey=%s&mobile=%s
    sendBulkSms: https://control.msg91.com/api/v5/flow/
    emailUrl: https://control.msg91.com/api/v5/email/send
    resultUrl: api/public/test-results/
    integratedNumber: 918520955547
    whatsAppOutBoundUrl: https://control.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/
    bulkwhatsAppOutBoundUrl: https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/
  telegram:
    token: **********************************************
    chatId: -422812765
  teacher:
    defaultSubjects: mathematics,english,evs,social,science,physics,Chemistry,biology
  batch:
    url: http://batch-service:8080/job
  #storageBucket: wexl-student-info - this is an important environment specific param
  courseContentBucket: wexledu-courses-nonprod
  tempPath: /tmp/
  device:
    single:
      organizations: rvm
    switch:
      organizations: vasavi
  sns:
    #topicArn: arn:aws:sns:ap-south-1:473077116069:wexl-retail-notifications-nonprod - this is an important environment specific param
    subscription:
      username: admin
      password: admin12345
    studentEvents:
      resultUrl: /#/studentview/view-result/
      correctionUrl: /#/test-correction/%s/%s
      wexlPracticeUrl: /#/learn/[subject_slug]/[chapter_slug]/[subtopic_slug]/[exam_type]/[exam_difficulty_level_id]/[grade_id]
      wexlTestUrl: /#/test/[subject_slug]/[chapter_slug]/null/[exam_type]/[exam_difficulty_level_id]/[grade_id]
      wexlSchoolTestUrl: /#/school-test/[test_definition_id]
  orgs:
    deletion:
      enabled: false
    cannedReport: sai681502
    allowRegistration: nal884925,ski694615
    allowRegistrationForCommerce: wexl-gitanjali,com784164
  latestAcademicYear: 25-26
  wordFinderUrl: https://api.dictionaryapi.dev/api/v2/entries/en/
  mobile:
    minimumVersion: 1.0.111
    notifications:
      wexlIconImage: https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/wexl_logo_5df21fe504.png
      newMlpNotification:
        notificationText: A New MLP has been created by the teacher!
        notificationTitle: New MLP Assigned
      newNotification:
        notificationText: A New %s has been created by the teacher!
        notificationTitle: New %s Assigned
  items:
    config:
      - url: https://www.google.com
        title: This is a sample description
        gradeSlug: viii
  sql:
    getTeacherCurriculum: |
      select distinct ts.board_slug, s.grade_id, ts.subject_slug ,s.uuid as sectionUuid  from teacher_subjects ts
      inner join sections s on ts.section_id=s.id
      where ts.teacher_id= :teacherId  and s.deleted_At is null
    getSectionMlpsDetails: |
      select m.id as mlp_id, concat(m.title, '(' ,to_char(m.created_at,'Mon DD'),')') as title,
      count(inst.exam_id) as section_attendance from mlp m
      inner join mlp_inst inst on inst.mlp_id = m.id
      where m.section_id = :sectionId and cast(to_char(m.created_at,'MM') as int) = :month
      and m.subject_slug = :subjectSlug and m.subtopic_slug is not null
      group by m.id
    getMlpReportsByStudentId: |
      select mi.student_id as studentId, m.id as mlpId, concat(m.title, '(' ,to_char(m.created_at,'Mon DD'),')') as title ,
      case when mi.exam_id is not null then true else false end as status, mi.knowledge_percentage as percentageScored
      from mlp m
      left join mlp_inst mi on mi.mlp_id = m.id
      where m.id in (:mlpIds) and mi.student_id in (:studentIds)
      order by m.id asc
  subject-profile-orgs:
    config:
      - id: 1
        source: wexl-gitanjali
        target: amr977, pudami
      - id: 2
        source: kri751
        target: pudami
      - id: 3
        source: mah177
        target: pudami
  mobileNumberLogin:
    users:
      limit: 4
  subjectsToIgnore: Literacyskills, GeneralAwareness, NumeracySkills, BookofPhonics, BookofHandwriting, Rhymesandsongs, Bookofstories
  ecommerce:
    service:
      url: http://product-service.dev.svc.cluster.local:8080/api/products/
    student:
      board: stdb
      grade: stdg
      section: stds
    stores:
      - slug: com784164
        type: shopify
        token: 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
        name: wexltsd
        productId: 2023-01
      - slug: lea999678
        type: shopify
        token: 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
        name: 05d164-2
        productId: 2023-01
management:
  endpoints:
    web:
      exposure:
        include: prometheus,health
  health:
    mail:
      enabled: false
jwt:
  tokenSecret: HHGDsdfdsfjk344fjs73jfh47984sdsdf4GSjskG273owyrbnsjrt46Ghdshdj29837HDjsdjhfdjh736567
  erpTokenSecret: HHGDsdfdsfjk344fjs73jayfh47984sdsdf4GSjsamkG273owyrbnsjrt46Ghdshdj29837HDjsdjhfdjh736567
  validityInDays: 60
  disabledEmails: <EMAIL>,<EMAIL>

reCaptcha:
  key: 6LdlFfYZAAAAADMsGeEzAgx0nOx6-pA69QqsceF7
  secret: 6LdlFfYZAAAAAIJltTcfH_H-J23wDDqaymJcz9Vv
  api: https://www.google.com/recaptcha/api/siteverify

urls:
  content: http://content-service:8080/content/

mobileApp:
  updatableApps:
    - com.wexledu.mobile.school
    - com.wexledu.mobile.school.kakatiya
    - com.wexledu.mobile.school.chanakya_highschool
    - com.wexledu.mobile.school.lotus
    - com.wexledu.mobile.school.mahajana
    - com.wexledu.mobile.school.matrusri
    - com.wexledu.mobile.school.neo_g
    - com.wexledu.mobile.school.paramhansa
    - com.wexledu.mobile.school.prose_edu
    - com.wexledu.mobile.school.scholars_home
    - com.wexledu.mobile.school.satyug
    - com.wexledu.mobile.school.vms
    - com.wexledu.mobile.school.crps
    - com.wexledu.mobile.school.d_w_p_school
    - com.wexledu.mobile.school.doon_school
    - com.wexledu.mobile.school.gis_learning

story-ladder:
  types:
    ChildhoodStories:
      - videoTitle: "The Bee and the Elephant"
        link: "https://www.youtube.com/watch?v=0K2GwHlcFPo"
        description: "A little bee teaches a big elephant a lesson in humility."
      - videoTitle: "Who is it"
        link: "https://www.youtube.com/watch?v=6APoTB1jQvI"
        description: ""
      - videoTitle: "Satya, Watch Out"
        link: "https://www.youtube.com/watch?v=yfzbVtThYYQ"
        description: ""
      - videoTitle: "Rabbit Becomes A Chef"
        link: "https://www.youtube.com/watch?v=KxEM8i3Gk0w"
        description: ""
      - videoTitle: "Goby's Noisy Best Friend"
        link: "https://www.youtube.com/watch?v=adRHXnhZ7b4"
        description: ""
      - videoTitle: "The Animal Meetings"
        link: "https://www.youtube.com/watch?v=E8aFsBxzFCA"
        description: ""
      - videoTitle: "Picnic and Pine Needles"
        link: "https://www.youtube.com/watch?v=w-csqM_XjrU"
        description: ""
      - videoTitle: "Why do Sun flowers Love the Sun"
        link: "https://www.youtube.com/watch?v=xk246E25OxQ"
        description: ""
      - videoTitle: "We All Need to Save Water"
        link: "https://www.youtube.com/watch?v=ebXAQvOp4us"
        description: ""
      - videoTitle: "Every Rupee Counts"
        link: "https://www.youtube.com/watch?v=OAPeh_aO1UU"
        description: ""
      - videoTitle: "How to Solve a Problem like Himani"
        link: "https://www.youtube.com/watch?v=fEBCZ1og24Y"
        description: ""
      - videoTitle: "You Won't Believe Me"
        link: "https://www.youtube.com/watch?v=Ici5I_FsNso"
        description: ""
      - videoTitle: "The Mystery of the Cyber Friend"
        link: "https://www.youtube.com/watch?v=Be7KYsiJv1o"
        description: ""
      - videoTitle: "How Pintu Found Pi"
        link: "https://www.youtube.com/watch?v=b-hCmpDSOj4"
        description: ""
      - videoTitle: "How many Travels"
        link: "https://www.youtube.com/watch?v=AyXKWdkmjh0"
        description: ""
      - videoTitle: "Everyday Superheroes"
        link: "https://www.youtube.com/watch?v=iZbmMjJSpec"
        description: ""
      - videoTitle: "Be Wise with Money"
        link: "https://www.youtube.com/watch?v=_lQl_Td6lAo"
        description: ""
      - videoTitle: "A stitch in time"
        link: "https://www.youtube.com/watch?v=k_oAvE2ihlY"
        description: ""
    InspirationalStories:
      - videoTitle: "Samsung's Success Story"
        link: "https://www.youtube.com/watch?v=8ftVACiJQu4"
        description: "This video tells the inspiring story of how Samsung grew from small beginnings to become one of the world’s top tech companies. By watching, you can practice listening for the main ideas and details while also learning useful words like innovation, expansion, and leadership."
      - videoTitle: "The Untold Story of Google"
        link: "https://www.youtube.com/watch?v=BhBJxFOMZgg"
        description: "This video unveils the inspiring journey of Google, from its humble beginnings in a Stanford dorm room to becoming a global tech giant. Through this story, you'll not only learn about Google's evolution but also enhance your English listening skills. It's an excellent resource for improving comprehension and expanding your vocabulary in a professional context."
      - videoTitle: "Sundar Pichai's Inspiring Story"
        link: "https://www.youtube.com/watch?v=oTimWHAoawU"
        description: "Discover the remarkable journey of Sundar Pichai—from his modest beginnings in Madurai, India, to leading Google and Alphabet as CEO. This video offers an inspiring narrative that not only motivates but also enhances your English listening skills.It's an excellent resource for enhancing listening comprehension and expanding vocabulary in a professional context."
      - videoTitle: "APJ Abdul Kalam's Story"
        link: "https://www.youtube.com/watch?v=y2LUA9LY1tU"
        description: "This video shares the inspiring journey of Dr. A.P.J. Abdul Kalam, from a humble childhood in Rameswaram to becoming India's ‘Missile Man’ and later the President. It will help you improve your English by understanding the flow of ideas in a story and by exploring new words such as perseverance, leadership, and determination."
      - videoTitle: "Warren Buffett's Story"
        link: "https://www.youtube.com/watch?v=lSFzupXF18g"
        description: "The life story of Warren Buffett—renowned investor and philanthropist—is compellingly narrated in this video. Learners will benefit from strengthening their discourse comprehension and expanding vocabulary with key terms such as investment, strategy, and perseverance."

