package com.wexl.ciniqai.controller;

import com.wexl.ciniqai.dto.PipeCatVoiceAgentDto;
import com.wexl.ciniqai.service.PipeCatVoiceAgentService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class PipeCatVoiceAgentController {
  private final PipeCatVoiceAgentService pipeCatVoiceAgentService;

  @PostMapping("/public/voice-sessions")
  public PipeCatVoiceAgentDto.Response startPipeCatVoiceSession() {
    return pipeCatVoiceAgentService.startPipeCatSession();
  }
}
