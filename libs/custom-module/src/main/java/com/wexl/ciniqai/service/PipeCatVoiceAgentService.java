package com.wexl.ciniqai.service;

import com.wexl.ciniqai.dto.PipeCatVoiceAgentDto;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@RequiredArgsConstructor
public class PipeCatVoiceAgentService {
  private final RestTemplate restTemplate;
  private static final String API_URL =
      "https://api.pipecat.daily.co/v1/public/pipecat-quickstart/start";
  private static final String AUTH_TOKEN = "Bearer pk_a949e166-9332-43f7-a634-a21ab9f4c5cd";

  public PipeCatVoiceAgentDto.Response startPipeCatSession() {
    // Set up headers
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.set("Authorization", AUTH_TOKEN);

    // Create request body
    Map<String, Object> requestBody = new HashMap<>();

    // Config with rtvi object
    Map<String, Object> config = new HashMap<>();
    config.put("rtvi", new HashMap<>());
    requestBody.put("config", config);

    // Set createDailyRoom to true
    requestBody.put("createDailyRoom", true);

    // Set dailyRoomProperties
    Map<String, Object> dailyRoomProperties = new HashMap<>();
    dailyRoomProperties.put("start_video_off", true);
    requestBody.put("dailyRoomProperties", dailyRoomProperties);

    // Set dailyMeetingTokenProperties
    Map<String, Object> dailyMeetingTokenProperties = new HashMap<>();
    dailyMeetingTokenProperties.put("is_owner", true);
    requestBody.put("dailyMeetingTokenProperties", dailyMeetingTokenProperties);

    // Set body with callerMeta
    Map<String, Object> body = new HashMap<>();
    Map<String, Object> callerMeta = new HashMap<>();
    callerMeta.put("device", "mobile-web");
    callerMeta.put("appVersion", "1.0.0");
    body.put("callerMeta", callerMeta);
    requestBody.put("body", body);

    // Create http entity with headers and body
    HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

    // Make the API call
    return restTemplate.postForObject(API_URL, entity, PipeCatVoiceAgentDto.Response.class);
  }
}
