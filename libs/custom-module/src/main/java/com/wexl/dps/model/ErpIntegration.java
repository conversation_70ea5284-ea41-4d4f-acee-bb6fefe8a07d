package com.wexl.dps.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "erp_integrations")
public class ErpIntegration extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private LocalDateTime lastSyncedAt;

  private String type; // can be TEACHER, STUDENT, PARENT

  @Column(name = "json", columnDefinition = "VARCHAR(10000)")
  private String json;
}
