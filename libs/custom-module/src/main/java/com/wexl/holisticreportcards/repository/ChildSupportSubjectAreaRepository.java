package com.wexl.holisticreportcards.repository;

import com.wexl.holisticreportcards.model.ChildSupportSubjectArea;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ChildSupportSubjectAreaRepository
    extends JpaRepository<ChildSupportSubjectArea, Long> {
  Optional<ChildSupportSubjectArea> findByStudentId(long studentId);
}
