package com.wexl.pallavi.reports;

import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.dps.reportcard.UpperGradeReportCard;
import com.wexl.pallavi.dto.Grade9Dto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.OfflineTestDefinitionRepository;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.repository.UpperGradeReportCardData;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class Grade9Report extends PallaviBaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;

  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final StudentAttributeService studentAttributeService;
  private final OfflineTestDefinitionRepository offlineTestDefinitionRepository;
  private final TeacherRepository teacherRepository;
  private final UpperGradeReportCard upperGradeReportCard;

  public List<String> pallaviSlugs =
      List.of("pal556078", "pal174599", "pal332908", "pal988947", "pal454783", "pal233196");
  public List<String> titles = List.of("1B", "2B");
  List<String> absentReasons = List.of("AB", "PA", "PL", "ML");

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = getPallaviGrade9Header(user.getStudentInfo(), org);
    var body = buildBody(user, org.getSlug(), request.offlineTestDefinitionId());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("gradeIX.xml");
  }

  private Grade9Dto.Body buildBody(User user, String orgSlug, Long offlineTestDefinitionId) {

    var student = user.getStudentInfo();
    var studentId = student.getId();
    var guardians = student.getGuardians();
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    var teacher = student.getSection().getClassTeacher();
    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            null, student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getUpperGradeReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);

    if (Objects.isNull(data)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }

    List<UpperGradeReportCardData> scholasticMandatoryData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();
    var optionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();
    var coScholasticMandatoryData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var coScholasticOptionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();

    return Grade9Dto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .className(student.getSection().getGradeName())
        .rollNumber(student.getClassRollNumber())
        .fathersName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .orgSlug(orgSlug)
        .gradeSlug(student.getSection().getGradeSlug())
        .dateOfBirth(studentAttributeService.getDateOfBirthFormat(dateOfBirth))
        .classTeacher(teacher != null ? teacher.getUserInfo().getFirstName() : null)
        .section(student.getSection().getName())
        .firstTable(buildFirstTable(scholasticMandatoryData, orgSlug))
        .secondTable(buildSecondTable(optionalData, orgSlug))
        .thirdTable(buildThirdTable(coScholasticOptionalData, orgSlug))
        .fourthTable(buildFourthTable(coScholasticMandatoryData, orgSlug))
        .attendance(buildAttendance(studentId))
        .build();
  }

  private Grade9Dto.FirstTable buildFirstTable(
      List<UpperGradeReportCardData> scholasticDataList, String orgSlug) {

    var tableMarks = buildTableMarks(scholasticDataList, orgSlug);

    var maxMarks = (tableMarks.size()) * 100;

    var overAllMarksOfAllSubjects =
        tableMarks.stream()
            .mapToDouble(marks -> Double.parseDouble(String.valueOf(marks.total())))
            .sum();

    var averageTotalMarks = overAllMarksOfAllSubjects / (tableMarks.size());

    var overAllGradeOfAllSubjects = calculateGrade(averageTotalMarks, 100);

    var percentage = (overAllMarksOfAllSubjects / maxMarks) * 100;

    var totalSubjectForPallavi =
        tableMarks.stream()
            .mapToDouble(marks -> Double.parseDouble(String.valueOf(marks.marksForPallavi())))
            .sum();

    String overAllMarksOfAllSubjectsForPallavi = String.format("%.2f", totalSubjectForPallavi);
    var averageTotalMarksOfPallavi = totalSubjectForPallavi / (tableMarks.size());
    var overAllGradeOfAllSubjectsForPallavi = calculateGrade(averageTotalMarksOfPallavi, 100);
    var percentageOfPallavi = (totalSubjectForPallavi / maxMarks) * 100;
    String overAllPercentageOfPallavi = String.format("%.2f", percentageOfPallavi);

    String overAllPercentage = String.format("%.2f", percentage);
    String overallMarks = String.format("%.2f", overAllMarksOfAllSubjects);

    var isMaxMarks = (tableMarks.size());

    var totalMarksOfInternalAssessment =
        tableMarks.stream()
            .mapToDouble(
                marks -> Double.parseDouble(String.valueOf(marks.internalAssessmentMarks())))
            .sum();
    var averageOfIsTotalMarks = totalMarksOfInternalAssessment / isMaxMarks;
    var totalInternalSubjectGrade = calculateGrade(averageOfIsTotalMarks, 20);

    String totalInternalSubjectMarks = String.format("%.2f", totalMarksOfInternalAssessment);

    var isPercentage = (totalMarksOfInternalAssessment / (isMaxMarks * 20)) * 100;
    var overallGrade = calculateGrade(isPercentage, 100);
    String overAllIsPercentage = String.format("%.2f", isPercentage);
    return Grade9Dto.FirstTable.builder()
        .title("SCHOLASTIC AREAS")
        .totalGrade(overAllGradeOfAllSubjects)
        .overallPercentage(overAllPercentage)
        .marks(tableMarks)
        .overAllMarksOfAllSubjects(overallMarks)
        .totalInternalSubjectMarks(totalInternalSubjectMarks)
        .totalInternalSubjectGrade(totalInternalSubjectGrade)
        .isPercentage(overAllIsPercentage)
        .overallInternalSubjectGrade(overallGrade)
        .overAllGradeOfAllSubjectsForPallavi(overAllGradeOfAllSubjectsForPallavi)
        .overAllMarksOfAllSubjectsForPallavi(overAllMarksOfAllSubjectsForPallavi)
        .overAllPercentageOfPallavi(overAllPercentageOfPallavi)
        .build();
  }

  private List<Grade9Dto.Marks> buildTableMarks(
      List<UpperGradeReportCardData> firstTableMarks, String orgSlug) {

    List<Grade9Dto.Marks> marksList = new ArrayList<>();

    var scholasticDataMap =
        firstTableMarks.stream()
            .sorted(Comparator.comparingLong(UpperGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    UpperGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    AtomicInteger sequenceCounter = new AtomicInteger(1);

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var pt1 = getPtsMarks("pa1", scholasticData);
          var pt2 = getPtsMarks("pa2", scholasticData);
          var pt3 = getMarks("pa3", scholasticData);
          var pt1SubjectMarks = getPtsSubjectMarks("pa1", scholasticData);
          var pt2SubjectMarks = getPtsSubjectMarks("pa2", scholasticData);
          var pt3SubjectMarks = getSubjectMarks("pa3", scholasticData);
          var pt = getPtMarks(List.of("pa1", "pa2", "pa3"), scholasticData);
          var ma = getMarks(Collections.singletonList("ma"), scholasticData);
          var se = getMarks(Collections.singletonList("se"), scholasticData);
          var po = getMarks(Collections.singletonList("portfolio"), scholasticData);
          var ae = getMarks(Collections.singletonList("ae"), scholasticData);

          String totalPtMarks =
              sumOfPtMarks(
                  pt1, pt2, pt3, orgSlug, pt1SubjectMarks, pt2SubjectMarks, pt3SubjectMarks);
          var ptMaSePoTotalForPallavi = sumMarks(ma, se, po, totalPtMarks);
          var marksForPallavi = sumMarks(ae, ptMaSePoTotalForPallavi);
          String gradeForPallavi = calculateGrade(Double.parseDouble(marksForPallavi), 100);

          var ptMaSePo = sumMarks(ma, se, po, pt);
          var ptMaSePoGrade = calculateGrade(Double.parseDouble(ptMaSePo), 20);

          String allExamsTotal = sumMarks(ae, ptMaSePo);

          String grade = calculateGrade(Double.parseDouble(allExamsTotal), 100);
          marksList.add(
              Grade9Dto.Marks.builder()
                  .sno(sequenceCounter.getAndIncrement())
                  .subject(subject)
                  .bestOfPt(pallaviSlugs.stream().anyMatch(orgSlug::contains) ? totalPtMarks : pt)
                  .ma(String.valueOf(ma))
                  .se(String.valueOf(se))
                  .po(String.valueOf(po))
                  .ae(String.valueOf(ae))
                  .internalAssessmentMarks(ptMaSePo)
                  .internalAssessmentGrade(ptMaSePoGrade)
                  .total(allExamsTotal)
                  .marksForPallavi(marksForPallavi)
                  .gradeForPallavi(gradeForPallavi)
                  .grade(grade)
                  .build());
        });

    return marksList;
  }

  public String getPtMarks(
      List<String> assessmentSlugs, List<UpperGradeReportCardData> subjectData) {

    List<UpperGradeReportCardData> filteredData =
        subjectData.stream()
            .filter(d -> assessmentSlugs.contains(d.getAssessmentSlug()))
            .filter(d -> d.getMarks() != null && d.getSubjectMarks() != null)
            .toList();

    if (filteredData.isEmpty()) {
      return null;
    }

    List<UpperGradeReportCardData> sortedData =
        filteredData.stream()
            .sorted(
                (d1, d2) -> {
                  double percentage1 = (d1.getMarks() / d1.getSubjectMarks()) * 100;
                  double percentage2 = (d2.getMarks() / d2.getSubjectMarks()) * 100;
                  return Double.compare(percentage2, percentage1);
                })
            .limit(2)
            .toList();

    double totalMarks = sortedData.stream().mapToDouble(UpperGradeReportCardData::getMarks).sum();
    double totalSubjectMarks =
        sortedData.stream().mapToDouble(UpperGradeReportCardData::getSubjectMarks).sum();

    double average = (totalMarks / totalSubjectMarks) * 5;

    return String.format("%.2f", average);
  }

  public String getMarks(List<String> assessmentSlug, List<UpperGradeReportCardData> subjectData) {
    Double average;
    var data =
        subjectData.stream().filter(d -> assessmentSlug.contains(d.getAssessmentSlug())).toList();
    if (data.isEmpty()) {
      return " ";
    }

    var isAttended = data.stream().filter(d -> d.getIsAttended() == null).toList();
    if (!isAttended.isEmpty()) {
      return " ";
    }

    var data1 =
        data.stream().filter(d -> Boolean.TRUE.equals(Boolean.valueOf(d.getIsAttended()))).toList();

    if (data1.isEmpty()) {
      return data.getFirst().getRemarks() == null
          ? "AB"
          : data.getFirst().getRemarks().substring(0, 2).toUpperCase();
    }

    average =
        data1.stream()
            .map(UpperGradeReportCardData::getMarks)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .average()
            .orElse(0.0);

    return String.format("%.2f", average);
  }

  private String sumOfPtMarks(
      Double pt1,
      Double pt2,
      Double pt3,
      String orgSlug,
      Double pt1SubjectMarks,
      Double pt2SubjectMarks,
      Double pt3SubjectMarks) {
    var pt1total = pt1SubjectMarks;
    var pt2total = pt2SubjectMarks;
    var pt3total = pt3SubjectMarks;

    for (String slug : pallaviSlugs) {
      if (slug.equals(orgSlug)) {
        var totalPtMarks = ((pt1 + pt2 + pt3) / (pt1total + pt3total + pt2total)) * 5;
        return String.format("%.2f", totalPtMarks);
      }
    }
    final double EPSILON = 1e-6;
    Double firstMax, secondMax;
    Double firstMaxTotal, secondMaxTotal;

    if (pt1 >= pt2 && pt1 >= pt3) {
      firstMax = pt1;
      firstMaxTotal = pt1total;
      secondMax = Math.max(pt2, pt3);
      secondMaxTotal = (pt2 >= pt3) ? pt2total : pt3total;
    } else if (pt2 >= pt1 && pt2 >= pt3) {
      firstMax = pt2;
      firstMaxTotal = pt2total;
      secondMax = Math.max(pt1, pt3);
      if (Math.abs(pt1 - pt3) < EPSILON && pt1 > 20) {
        secondMaxTotal = pt3total;
      } else {
        secondMaxTotal = (pt1 >= pt3) ? pt1total : pt3total;
      }
    } else {
      firstMax = pt3;
      firstMaxTotal = pt3total;
      secondMax = Math.max(pt1, pt2);
      secondMaxTotal = (pt1 >= pt2) ? pt1total : pt2total;
    }
    var sumOfPts =
        (((firstMax / firstMaxTotal) * 2.5)
            + ((secondMax > 20.0)
                ? ((secondMax / 40) * 2.5)
                : ((secondMax / secondMaxTotal) * 2.5)));
    return String.format("%.2f", sumOfPts);
  }

  private String calculateGrade(Double marks, double totalMarks) {
    return marks == null || Objects.isNull(totalMarks)
        ? null
        : pointScaleEvaluator.evaluate("8point", BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private String sumMarks(String... marks) {
    double sum =
        Arrays.stream(marks)
            .filter(Objects::nonNull)
            .map(mark -> mark.matches("-?\\d+(\\.\\d+)?") ? Double.parseDouble(mark) : 0.0)
            .mapToDouble(Double::doubleValue)
            .sum();
    return String.format("%.2f", sum);
  }

  private Double getAverageMarks(
      String assessmentSlug,
      List<UpperGradeReportCardData> subjectData,
      Function<UpperGradeReportCardData, Number> marksExtractor,
      boolean checkTitle) {
    return subjectData.stream()
        .filter(
            data ->
                assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug())
                    && (checkTitle
                        || titles.stream()
                            .anyMatch(title -> data.getTitle().toUpperCase().contains(title))))
        .map(marksExtractor)
        .filter(Objects::nonNull)
        .mapToDouble(Number::doubleValue)
        .average()
        .orElse(0.0);
  }

  private Double getMarks(String assessmentSlug, List<UpperGradeReportCardData> subjectData) {
    return getAverageMarks(assessmentSlug, subjectData, UpperGradeReportCardData::getMarks, true);
  }

  private Double getPtsMarks(String assessmentSlug, List<UpperGradeReportCardData> subjectData) {
    return getAverageMarks(assessmentSlug, subjectData, UpperGradeReportCardData::getMarks, false);
  }

  private Double getSubjectMarks(
      String assessmentSlug, List<UpperGradeReportCardData> subjectData) {
    return getAverageMarks(
        assessmentSlug, subjectData, UpperGradeReportCardData::getSubjectMarks, true);
  }

  private Double getPtsSubjectMarks(
      String assessmentSlug, List<UpperGradeReportCardData> subjectData) {
    return getAverageMarks(
        assessmentSlug, subjectData, UpperGradeReportCardData::getSubjectMarks, false);
  }

  private Grade9Dto.SecondTable buildSecondTable(
      List<UpperGradeReportCardData> optionalData, String orgSlug) {
    return Grade9Dto.SecondTable.builder()
        .title("Additional Subjects")
        .secondTableValues(buildSecondTableValues(optionalData, orgSlug))
        .build();
  }

  private List<Grade9Dto.SecondTableValues> buildSecondTableValues(
      List<UpperGradeReportCardData> optionalData, String orgSlug) {

    Map<String, String> subjectHighestMarksMap = new HashMap<>();
    List<Grade9Dto.SecondTableValues> secondTableData = new ArrayList<>();
    if (pallaviSlugs.contains(orgSlug)) {

      for (UpperGradeReportCardData data : optionalData) {
        var subject = data.getSubjectName();
        String marks = null;
        if (data.getAssessmentSlug().equals("ae")) {
          marks = getMarks(data);
        }

        if (marks != null) {
          subjectHighestMarksMap.put(subject, marks);
        }
      }

      for (Map.Entry<String, String> entry : subjectHighestMarksMap.entrySet()) {
        var subject = entry.getKey();
        var highestMarks = entry.getValue();

        var grade =
            absentReasons.contains(highestMarks)
                ? "E"
                : pointScaleEvaluator.evaluate(
                    "8point", BigDecimal.valueOf(Double.valueOf(highestMarks)));

        secondTableData.add(
            Grade9Dto.SecondTableValues.builder()
                .subject(subject)
                .marks(highestMarks)
                .grade(grade)
                .build());
      }
    } else {
      Map<String, List<UpperGradeReportCardData>> subjectDataMap =
          optionalData.stream()
              .collect(Collectors.groupingBy(UpperGradeReportCardData::getSubjectName));

      for (Map.Entry<String, List<UpperGradeReportCardData>> entry : subjectDataMap.entrySet()) {
        String subject = entry.getKey();
        List<UpperGradeReportCardData> subjectData = entry.getValue();

        String scolosticOptionalMarks =
            getScolosticOptionalMarks(List.of("pa1", "pa2", "pa3"), subjectData);

        if (scolosticOptionalMarks != null) {
          var grade =
              pointScaleEvaluator.evaluate(
                  "8point", BigDecimal.valueOf(Double.valueOf(scolosticOptionalMarks)));

          secondTableData.add(
              Grade9Dto.SecondTableValues.builder()
                  .subject(subject)
                  .marks(scolosticOptionalMarks)
                  .grade(grade)
                  .build());
        }
      }
    }
    return secondTableData;
  }

  public String getMarks(UpperGradeReportCardData subjectData) {

    if (subjectData.getIsAttended() == null) {
      return null;
    }

    if (Boolean.FALSE.equals(Boolean.valueOf(subjectData.getIsAttended()))) {
      return subjectData.getRemarks() == null
          ? "AB"
          : subjectData.getRemarks().substring(0, 2).toUpperCase();
    }

    var marks = subjectData.getActualMarks() / subjectData.getSubjectMarks().doubleValue() * 100;

    return String.format("%.2f", marks);
  }

  public String getScolosticOptionalMarks(
      List<String> assessmentSlugs, List<UpperGradeReportCardData> subjectData) {

    List<UpperGradeReportCardData> filteredData =
        subjectData.stream()
            .filter(d -> assessmentSlugs.contains(d.getAssessmentSlug()))
            .filter(d -> d.getMarks() != null && d.getSubjectMarks() != null)
            .toList();

    if (filteredData.isEmpty()) {
      return null;
    }
    List<UpperGradeReportCardData> sortedData =
        filteredData.stream()
            .sorted((d1, d2) -> Double.compare(d2.getMarks(), d1.getMarks()))
            .limit(2)
            .toList();

    List<UpperGradeReportCardData> sortenedData =
        sortedData.stream()
            .sorted(
                (d1, d2) -> {
                  double percentage2 = (d2.getMarks() / d2.getSubjectMarks()) * 2.5;
                  double percentage1 = (d1.getMarks() / d1.getSubjectMarks()) * 2.5;
                  return Double.compare(percentage2, percentage1);
                })
            .limit(2)
            .toList();

    double totalMarks =
        sortenedData.stream()
            .mapToDouble(data -> (data.getMarks() / data.getSubjectMarks()) * 2.5)
            .sum();

    return String.format("%.2f", totalMarks);
  }

  private Grade9Dto.ThirdTable buildThirdTable(
      List<UpperGradeReportCardData> coScholasticMandatoryData, String orgSlug) {
    return Grade9Dto.ThirdTable.builder()
        .title("Co Scholastic Areas[on a 5-point (A-E) grading scale]")
        .thirdTableValues(buildThirdTableValues(coScholasticMandatoryData, orgSlug))
        .build();
  }

  private Grade9Dto.FourthTable buildFourthTable(
      List<UpperGradeReportCardData> coScholasticMandatoryData, String orgSlug) {
    return Grade9Dto.FourthTable.builder()
        .title("Co Scholastic Areas[on a 3-point (A-C) grading scale]")
        .fourthTableValues(buildFourthTableValues(coScholasticMandatoryData, orgSlug))
        .build();
  }

  private List<Grade9Dto.ThirdTableValues> buildFourthTableValues(
      List<UpperGradeReportCardData> coScholasticMandatoryData, String orgSlug) {

    List<Grade9Dto.ThirdTableValues> thirdTableData = new ArrayList<>();

    for (UpperGradeReportCardData data : coScholasticMandatoryData) {

      var subject = data.getSubjectName();
      var marks = data.getMarks();

      String grade =
          Objects.isNull(marks)
              ? "N/A"
              : pointScaleEvaluator.evaluate(
                  pallaviSlugs.stream().anyMatch(orgSlug::contains) ? "3point" : "8point",
                  BigDecimal.valueOf(marks));

      thirdTableData.add(
          Grade9Dto.ThirdTableValues.builder().subject(subject).grade(grade).build());
    }

    return thirdTableData;
  }

  private List<Grade9Dto.ThirdTableValues> buildThirdTableValues(
      List<UpperGradeReportCardData> coScholasticMandatoryData, String orgSlug) {

    List<Grade9Dto.ThirdTableValues> thirdTableData = new ArrayList<>();

    for (UpperGradeReportCardData data : coScholasticMandatoryData) {

      var subject = data.getSubjectName();
      var marks = data.getMarks();

      String grade =
          Objects.isNull(marks)
              ? "N/A"
              : pointScaleEvaluator.evaluate(
                  pallaviSlugs.stream().anyMatch(orgSlug::contains) ? "5point" : "8point",
                  BigDecimal.valueOf(marks));

      thirdTableData.add(
          Grade9Dto.ThirdTableValues.builder().subject(subject).grade(grade).build());
    }

    return thirdTableData;
  }

  private Grade9Dto.Attendance buildAttendance(Long studentId) {

    var termAssessment = termAssessmentRepository.findBySlug("ae");

    var studentAttendance =
        offlineTestScheduleStudentAttendanceRepository.getOfflineTestStudentAttendanceAscOrder(
            studentId, termAssessment.get().getId());

    if (studentAttendance.isEmpty()) {
      return Grade9Dto.Attendance.builder().build();
    }

    var remarks =
        (!studentAttendance.isEmpty() && studentAttendance.get().getRemarks() != null)
            ? studentAttendance.get().getRemarks()
            : " ";

    var testDefinition = studentAttendance.get().getOfflineTestDefinition();
    if (testDefinition == null) {
      throw new IllegalArgumentException("Test definition not found for studentId: " + studentId);
    }

    var totalAttendanceDays = testDefinition.getTotalAttendanceDays();
    Long daysPresent = studentAttendance.get().getPresentDays();
    if (totalAttendanceDays == null || daysPresent == null) {
      return Grade9Dto.Attendance.builder().build();
    }

    String attendancePercentage = null;
    var totalDays = Double.parseDouble(totalAttendanceDays);
    if (totalDays > 0.0) {
      var attendancePerc = (daysPresent.doubleValue() / totalDays) * 100;
      attendancePercentage = String.format("%.2f", attendancePerc);
    }
    return Grade9Dto.Attendance.builder()
        .title("Attendance")
        .remarks(remarks)
        .totalWorkingDays(Long.valueOf(totalAttendanceDays))
        .daysPresent(daysPresent)
        .attendancePercentage(attendancePercentage)
        .build();
  }
}
