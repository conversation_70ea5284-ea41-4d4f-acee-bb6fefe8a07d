package com.wexl.gilico.service;

import com.wexl.dps.dto.GuardianCardDto;
import com.wexl.holisticreportcards.ProgressCardService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.offlinetest.service.ReportCardTemplateService;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.profile.ProfileService;
import com.wexl.retail.util.ValidationUtils;
import jakarta.transaction.Transactional;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

@Service
@Slf4j
@RequiredArgsConstructor
public class GillcoParentIdCard {
  private final ReportCardService reportCardService;
  private final ValidationUtils validationUtils;
  private final StudentRepository studentRepository;
  private final ReportCardTemplateService reportCardTemplateService;
  private final StorageService storageService;
  private final TemplateEngine templateEngine;
  private final ProgressCardService progressCardService;
  private final ProfileService profileService;
  private final GuardianService guardianService;

  @Async
  @Transactional
  public void generateGuardianCard(
      String orgSlug, String gradeSlug, String boardSlug, String sectionUuid) {
    var section = validationUtils.findSectionByUuid(sectionUuid);
    var org = validationUtils.isOrgValid(orgSlug);
    var students =
        studentRepository.getStudentsByBoardAndGradeAndSection(
            org.getSlug(), boardSlug, gradeSlug, section.getUuid());
    var pdfList = new ArrayList<byte[]>();

    for (int i = 0; i < students.size(); i++) {
      var student = students.get(i);
      try {
        var header = buildGilcoParentHeader(student, org);

        Map<String, Object> model = new HashMap<>();
        model.put("header", header);

        var context = new Context(Locale.getDefault(), Map.of("model", model));
        String foTemplate = templateEngine.process("report-card/dps/gillco-id-card.xml", context);
        byte[] pdf = progressCardService.generatePdf(foTemplate);
        var path =
            String.format(
                "guardian-card/%s/%s/%s/%s/%s.pdf",
                orgSlug,
                boardSlug,
                gradeSlug,
                section.getUuid(),
                student.getUserInfo().getAuthUserId());
        storageService.uploadFile(pdf, path, MediaType.APPLICATION_PDF_VALUE);

        if (i + 1 < students.size()) {
          var student2 = students.get(i + 1);
          var header1 = buildGilcoParentHeader(student2, org);

          Map<String, Object> model2 = new HashMap<>();
          model2.put("header", header);
          model2.put("header1", header1);

          var context2 = new Context(Locale.getDefault(), Map.of("model", model2));
          String foTemplate2 =
              templateEngine.process("report-card/dps/gillco-bulk-id-card.xml", context2);
          byte[] pdf2 = progressCardService.generatePdf(foTemplate2);
          var path2 =
              String.format(
                  "guardian-card/%s/%s/%s/%s/%s_%s.pdf",
                  orgSlug,
                  boardSlug,
                  gradeSlug,
                  section.getUuid(),
                  student.getUserInfo().getAuthUserId(),
                  student2.getUserInfo().getAuthUserId());
          storageService.uploadFile(pdf2, path2, MediaType.APPLICATION_PDF_VALUE);

          pdfList.add(pdf2);
          i++;
        } else {
          Map<String, Object> model2 = new HashMap<>();
          model2.put("header", header);
          model2.put("header1", null);

          var context2 = new Context(Locale.getDefault(), Map.of("model", model2));
          String foTemplate2 =
              templateEngine.process("report-card/dps/gillco-bulk-id-card.xml", context2);
          byte[] pdf2 = progressCardService.generatePdf(foTemplate2);
          var path2 =
              String.format(
                  "guardian-card/%s/%s/%s/%s/%s_null.pdf",
                  orgSlug,
                  boardSlug,
                  gradeSlug,
                  section.getUuid(),
                  student.getUserInfo().getAuthUserId());
          storageService.uploadFile(pdf2, path2, MediaType.APPLICATION_PDF_VALUE);

          pdfList.add(pdf2);
        }
      } catch (Exception e) {
        log.error(
            "Error in generating guardian card for student: "
                + student.getUserInfo().getAuthUserId(),
            e);
      }
    }

    var mergedPdf = reportCardTemplateService.mergeReportCards(pdfList);
    var sectionPath =
        String.format(
            "guardian-card/%s/%s/%s/%s/section.pdf",
            orgSlug, boardSlug, gradeSlug, section.getUuid());
    storageService.uploadFile(mergedPdf, sectionPath, MediaType.APPLICATION_PDF_VALUE);
  }

  private GuardianCardDto.Header buildGilcoParentHeader(Student studentInfo, Organization org) {
    var guardians = studentInfo.getGuardians();
    Optional<StudentAttributeValueModel> dateOfBirthOpt =
        reportCardService.getStudentAttributeValue(studentInfo, "date_of_birth");
    var dateOfBirth = dateOfBirthOpt.map(StudentAttributeValueModel::getValue).orElse(null);
    Optional<StudentAttributeValueModel> address =
        reportCardService.getStudentAttributeValue(studentInfo, "residential_address");
    var father =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.FATHER)).findAny();

    var mother =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.MOTHER)).findAny();

    var guardian =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.GUARDIAN)).findAny();
    return GuardianCardDto.Header.builder()
        .studentName(
            studentInfo.getUserInfo().getFirstName()
                + " "
                + studentInfo.getUserInfo().getLastName())
        .rollNo(studentInfo.getRollNumber())
        .sectionName(studentInfo.getSection().getName())
        .admissionNo(studentInfo.getRollNumber())
        .fatherName(father.map(x -> x.getFirstName() + " " + x.getLastName()).orElse(null))
        .motherName(mother.map(x -> x.getFirstName() + " " + x.getLastName()).orElse(null))
        .fatherNumber(father.map(Guardian::getMobileNumber).orElse(null))
        .motherNumber(mother.map(Guardian::getMobileNumber).orElse(null))
        .guardianName(guardian.map(x -> x.getFirstName() + " " + x.getLastName()).orElse(null))
        .guardianNumber(guardian.map(Guardian::getMobileNumber).orElse(null))
        .academicYear(studentInfo.getAcademicYearSlug())
        .studentPhoto(
            Objects.isNull(studentInfo.getUserInfo().getProfileImage())
                ? null
                : profileService.getProfileImageUrl(studentInfo.getUserInfo().getProfileImage()))
        .guardianPhoto(
            guardian.map(value -> guardianService.fetchImage(value.getImageUrl())).orElse(null))
        .fatherPhoto(
            father.map(value -> guardianService.fetchImage(value.getImageUrl())).orElse(null))
        .motherPhoto(
            mother.map(value -> guardianService.fetchImage(value.getImageUrl())).orElse(null))
        .dob(dateOfBirth)
        .address(
            address
                .map(StudentAttributeValueModel::getValue)
                .filter(val -> val != null && !val.isBlank())
                .orElse("-"))
        .build();
  }

  public String getGuardianCard(
      String orgSlug,
      String studentAuthId,
      String gradeSlug,
      String boardSlug,
      String sectionUuid) {
    try {
      if (studentAuthId == null) {
        return storageService.generatePreSignedUrlForFetch(
            String.format(
                "guardian-card/%s/%s/%s/%s/section.pdf",
                orgSlug, boardSlug, gradeSlug, sectionUuid));
      }
      return storageService.generatePreSignedUrlForFetch(
          String.format(
              "guardian-card/%s/%s/%s/%s/%s.pdf",
              orgSlug, boardSlug, gradeSlug, sectionUuid, studentAuthId));
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.GuardianCardNotFound");
    }
  }
}
