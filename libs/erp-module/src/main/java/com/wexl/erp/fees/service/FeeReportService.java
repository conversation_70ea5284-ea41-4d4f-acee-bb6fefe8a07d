package com.wexl.erp.fees.service;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.util.CsvUtils;
import jakarta.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FeeReportService {

  private final FeeHeadRepository feeHeadRepository;
  private final DateTimeUtil dateTimeUtil;

  private static final List<String> BASIC_STUDENT_HEADERS = Arrays.asList(
      "Sno", "Admission Number", "Name of Student", "Father Name", "Father Mobile No",
      "Mother Name", "Roll No", "Mother Mobile No", "Class", "Gender", "Student Category",
      "Date of Admission", "Student Status");

  private static final List<String> STUDENT_TERM_WISE_HEADERS = Arrays.asList(
      "Student Name", "Student Registration Id", "Class", "Father Name",
      "Class Roll Number", "Student Status", "Fee Head", "Term", "Amount", "Due Date");

  public void generateFeeReportCsv(
      String orgSlug, FeeDto.FeeReportRequest request, HttpServletResponse response) {
    
    String reportType = request.reportType();
    if ("fee_head_master".equals(reportType)) {
      generateFeeHeadMasterReportCsv(orgSlug, request, response);
    } else if ("student_term_wise".equals(reportType)) {
      generateStudentTermWiseReportCsv(orgSlug, request, response);
    } else {
      throw new IllegalArgumentException("Invalid report type: " + reportType);
    }
  }

  private void generateFeeHeadMasterReportCsv(
      String orgSlug, FeeDto.FeeReportRequest request, HttpServletResponse response) {
    
    List<Object[]> rawData = feeHeadRepository.findFeeHeadMasterReportData(
        orgSlug, request.academicYear(), request.sectionUuids());
    
    // Process data to group by student and fee types
    Map<Long, StudentFeeData> studentDataMap = processFeeHeadMasterData(rawData);
    
    // Generate two-row headers based on fee types found in data
    HeaderStructure headerStructure = generateFeeHeadMasterHeaders(studentDataMap);
    
    // Build CSV data with two-row headers
    List<List<String>> csvData = buildFeeHeadMasterCsvData(studentDataMap, headerStructure);
    
    // Generate CSV response with two-row headers
    generateTwoRowHeaderCsvResponse(csvData, headerStructure, "Fee_Head_Master_Report.csv", response);
  }

  private void generateStudentTermWiseReportCsv(
      String orgSlug, FeeDto.FeeReportRequest request, HttpServletResponse response) {
    
    List<Object[]> rawData = feeHeadRepository.findStudentTermWiseReportData(
        orgSlug, request.academicYear(), request.term(), 
        request.classFilter(), request.studentFilter());
    
    List<FeeDto.FeeReportResponse> reportData = processStudentTermWiseData(rawData);
    
    // Build CSV data
    List<List<String>> csvData = buildStudentTermWiseCsvData(reportData);
    
    // Generate CSV response with single header
    generateSingleHeaderCsvResponse(csvData, STUDENT_TERM_WISE_HEADERS, "Student_Term_Wise_Report.csv", response);
  }

  private Map<Long, StudentFeeData> processFeeHeadMasterData(List<Object[]> rawData) {
    Map<Long, StudentFeeData> studentDataMap = new HashMap<>();
    
    int sno = 1;
    for (Object[] row : rawData) {
      Long studentId = ((Number) row[0]).longValue();
      
      if (!studentDataMap.containsKey(studentId)) {
        StudentFeeData studentData = new StudentFeeData();
        studentData.sno = sno++;
        studentData.admissionNumber = (String) row[1];
        studentData.studentName = (String) row[2];
        studentData.fatherName = (String) row[3];
        studentData.fatherMobile = (String) row[4];
        studentData.motherName = (String) row[5];
        studentData.motherMobile = (String) row[6];
        studentData.rollNumber = (String) row[7];
        studentData.classRollNumber = (String) row[8];
        studentData.className = (String) row[9];
        studentData.gender = (String) row[10];
        studentData.studentCategory = (String) row[11];
        studentData.dateOfAdmission = (String) row[12];
        studentData.studentStatus = (String) row[13];
        studentData.feeApplicable = new HashMap<>();
        studentData.feeCollected = new HashMap<>();
        
        studentDataMap.put(studentId, studentData);
      }
      
      // Process fee data if available
      if (row[14] != null) { // fee_type_name
        String feeTypeName = (String) row[14];
        Double feeAmount = row[15] != null ? ((Number) row[15]).doubleValue() : 0.0;
        Double paidAmount = row[22] != null ? ((Number) row[22]).doubleValue() : 0.0;
        
        StudentFeeData studentData = studentDataMap.get(studentId);
        studentData.feeApplicable.put(feeTypeName, feeAmount);
        
        if (paidAmount > 0) {
          String paymentDate = (String) row[20];
          String receiptNumber = (String) row[21];
          
          if (!studentData.feeCollected.containsKey(feeTypeName)) {
            studentData.feeCollected.put(feeTypeName, new ArrayList<>());
          }
          
          PaymentDetail payment = new PaymentDetail();
          payment.date = paymentDate != null ? paymentDate : "";
          payment.receiptNumber = receiptNumber != null ? receiptNumber : "";
          payment.amount = paidAmount;
          
          studentData.feeCollected.get(feeTypeName).add(payment);
        }
      }
    }
    
    // Calculate totals for each student
    for (StudentFeeData studentData : studentDataMap.values()) {
      studentData.grandTotal = studentData.feeApplicable.values().stream()
          .mapToDouble(Double::doubleValue).sum();
      studentData.totalCollected = studentData.feeCollected.values().stream()
          .flatMap(List::stream)
          .mapToDouble(payment -> payment.amount)
          .sum();
      studentData.dueAmount = studentData.grandTotal - studentData.totalCollected;
      studentData.discounts = 0.0; // TODO: Calculate actual discounts
    }
    
    return studentDataMap;
  }

  private HeaderStructure generateFeeHeadMasterHeaders(Map<Long, StudentFeeData> studentDataMap) {
    // Get all unique fee types from the data
    Set<String> feeTypes = studentDataMap.values().stream()
        .flatMap(student -> student.feeApplicable.keySet().stream())
        .collect(Collectors.toCollection(LinkedHashSet::new));
    
    HeaderStructure structure = new HeaderStructure();
    
    // Row 1: Main headers
    List<String> mainHeaders = new ArrayList<>(BASIC_STUDENT_HEADERS);
    mainHeaders.add("FEE APPLICABLE");
    mainHeaders.add(""); // Total
    mainHeaders.add(""); // Discounts  
    mainHeaders.add(""); // Grand Total
    mainHeaders.add("FEE COLLECTED");
    
    // Add fee type main headers for collected section
    for (String feeType : feeTypes) {
      mainHeaders.add(feeType);
      mainHeaders.add(""); // Date sub-column
      mainHeaders.add(""); // Receipt sub-column
    }
    mainHeaders.add(""); // Total Collected
    mainHeaders.add(""); // Due
    
    // Row 2: Sub headers
    List<String> subHeaders = new ArrayList<>();
    // Basic student info - no sub headers needed, use empty strings
    for (int i = 0; i < BASIC_STUDENT_HEADERS.size(); i++) {
      subHeaders.add("");
    }
    
    // Fee applicable sub headers
    for (String feeType : feeTypes) {
      subHeaders.add(feeType);
    }
    subHeaders.add("Total");
    subHeaders.add("Discounts");
    subHeaders.add("Grand Total");
    
    // Fee collected sub headers
    for (String feeType : feeTypes) {
      subHeaders.add("Date");
      subHeaders.add("Receipt No");
      subHeaders.add("Amount");
    }
    subHeaders.add("Total");
    subHeaders.add("Due");
    
    structure.mainHeaders = mainHeaders;
    structure.subHeaders = subHeaders;
    structure.feeTypes = new ArrayList<>(feeTypes);
    
    return structure;
  }

  // Helper classes for data structure
  private static class StudentFeeData {
    int sno;
    String admissionNumber;
    String studentName;
    String fatherName;
    String fatherMobile;
    String motherName;
    String motherMobile;
    String rollNumber;
    String classRollNumber;
    String className;
    String gender;
    String studentCategory;
    String dateOfAdmission;
    String studentStatus;
    Map<String, Double> feeApplicable;
    Map<String, List<PaymentDetail>> feeCollected;
    Double grandTotal;
    Double totalCollected;
    Double dueAmount;
    Double discounts;
  }
  
  private static class PaymentDetail {
    String date;
    String receiptNumber;
    Double amount;
  }
  
  private static class HeaderStructure {
    List<String> mainHeaders;
    List<String> subHeaders;
    List<String> feeTypes;
  }

  private List<FeeDto.FeeReportResponse> processStudentTermWiseData(List<Object[]> rawData) {
    return rawData.stream()
        .map(row -> FeeDto.FeeReportResponse.builder()
            .studentName((String) row[0])
            .admissionNumber((String) row[1]) // Using as registration ID
            .className((String) row[2])
            .fatherName((String) row[3])
            .rollNumber((String) row[4]) // Using class roll number
            .studentStatus((String) row[5])
            .feeHead((String) row[6])
            .term((String) row[7])
            .amount(row[8] != null ? ((Number) row[8]).doubleValue() : 0.0)
            .dueDate(formatDate(row[9]))
            .build())
        .collect(Collectors.toList());
  }

  private String formatDate(Object dateObj) {
    if (dateObj == null) return "";
    if (dateObj instanceof LocalDateTime) {
      return ((LocalDateTime) dateObj).format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));
    }
    return dateObj.toString();
  }

  private String formatAmount(Double amount) {
    return amount != null ? String.format("%.0f", amount) : "0";
  }

  private List<List<String>> buildFeeHeadMasterCsvData(
      Map<Long, StudentFeeData> studentDataMap, HeaderStructure headerStructure) {

    List<List<String>> csvData = new ArrayList<>();

    for (StudentFeeData studentData : studentDataMap.values()) {
      List<String> row = new ArrayList<>();

      // Basic student info
      row.add(String.valueOf(studentData.sno));
      row.add(studentData.admissionNumber != null ? studentData.admissionNumber : "");
      row.add(studentData.studentName != null ? studentData.studentName : "");
      row.add(studentData.fatherName != null ? studentData.fatherName : "");
      row.add(studentData.fatherMobile != null ? studentData.fatherMobile : "");
      row.add(studentData.motherName != null ? studentData.motherName : "");
      row.add(studentData.rollNumber != null ? studentData.rollNumber : "");
      row.add(studentData.motherMobile != null ? studentData.motherMobile : "");
      row.add(studentData.className != null ? studentData.className : "");
      row.add(studentData.gender != null ? studentData.gender : "");
      row.add(studentData.studentCategory != null ? studentData.studentCategory : "");
      row.add(studentData.dateOfAdmission != null ? studentData.dateOfAdmission : "");
      row.add(studentData.studentStatus != null ? studentData.studentStatus : "");

      // Fee applicable amounts
      for (String feeType : headerStructure.feeTypes) {
        Double amount = studentData.feeApplicable.getOrDefault(feeType, 0.0);
        row.add(formatAmount(amount));
      }

      row.add(formatAmount(studentData.grandTotal));
      row.add(formatAmount(studentData.discounts));
      row.add(formatAmount(studentData.grandTotal - studentData.discounts));

      // Fee collected amounts (Date, Receipt No, Amount for each fee type)
      for (String feeType : headerStructure.feeTypes) {
        List<PaymentDetail> payments = studentData.feeCollected.getOrDefault(feeType, new ArrayList<>());
        if (!payments.isEmpty()) {
          PaymentDetail firstPayment = payments.get(0);
          row.add(firstPayment.date);
          row.add(firstPayment.receiptNumber);
          row.add(formatAmount(firstPayment.amount));
        } else {
          row.add(""); // Date
          row.add(""); // Receipt No
          row.add("0"); // Amount
        }
      }

      row.add(formatAmount(studentData.totalCollected));
      row.add(formatAmount(studentData.dueAmount));

      csvData.add(row);
    }

    return csvData;
  }

  private List<List<String>> buildStudentTermWiseCsvData(List<FeeDto.FeeReportResponse> reportData) {
    return reportData.stream()
        .map(response -> Arrays.asList(
            response.studentName() != null ? response.studentName() : "",
            response.admissionNumber() != null ? response.admissionNumber() : "",
            response.className() != null ? response.className() : "",
            response.fatherName() != null ? response.fatherName() : "",
            response.rollNumber() != null ? response.rollNumber() : "",
            response.studentStatus() != null ? response.studentStatus() : "",
            response.feeHead() != null ? response.feeHead() : "",
            response.term() != null ? response.term() : "",
            formatAmount(response.amount()),
            response.dueDate() != null ? response.dueDate() : ""
        ))
        .collect(Collectors.toList());
  }

  private void generateTwoRowHeaderCsvResponse(
      List<List<String>> csvData, HeaderStructure headerStructure, String fileName, HttpServletResponse response) {

    response.setContentType("text/csv; charset=UTF-8");
    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

    try {
      var writer = response.getWriter();

      // Write main headers (Row 1)
      writer.println(String.join(",", headerStructure.mainHeaders.stream()
          .map(this::escapeCsvValue)
          .collect(Collectors.toList())));

      // Write sub headers (Row 2)
      writer.println(String.join(",", headerStructure.subHeaders.stream()
          .map(this::escapeCsvValue)
          .collect(Collectors.toList())));

      // Write data rows
      for (List<String> row : csvData) {
        writer.println(String.join(",", row.stream()
            .map(this::escapeCsvValue)
            .collect(Collectors.toList())));
      }

      writer.flush();
    } catch (Exception e) {
      log.error("Error generating CSV response", e);
      throw new RuntimeException("Error generating CSV response", e);
    }
  }

  private void generateSingleHeaderCsvResponse(
      List<List<String>> csvData, List<String> headers, String fileName, HttpServletResponse response) {

    response.setContentType("text/csv; charset=UTF-8");
    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

    if (csvData.isEmpty()) {
      CsvUtils.generateCsv(headers.toArray(new String[0]), new ArrayList<>(), response);
      return;
    }

    CsvUtils.generateCsv(headers.toArray(new String[0]), csvData, response);
  }

  private String escapeCsvValue(String value) {
    if (value == null) return "";
    if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
      return "\"" + value.replace("\"", "\"\"") + "\"";
    }
    return value;
  }
}
