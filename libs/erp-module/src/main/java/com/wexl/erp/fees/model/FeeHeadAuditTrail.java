package com.wexl.erp.fees.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "fee_head_audit_trails")
public class FeeHeadAuditTrail extends Model {
  @Id @GeneratedValue private UUID id;

  @Column(name = "org_slug", nullable = false)
  private String orgSlug;

  @Column(name = "entity_name", nullable = false)
  private String entityName;

  @Column(name = "entity_id", nullable = false)
  private String entityId;

  private String reason;

  @Column(columnDefinition = "TEXT")
  private String oldValue;

  @Column(columnDefinition = "TEXT")
  private String newValue;

  @Column(name = "updated_by", nullable = false)
  private Long updatedBy;
}
