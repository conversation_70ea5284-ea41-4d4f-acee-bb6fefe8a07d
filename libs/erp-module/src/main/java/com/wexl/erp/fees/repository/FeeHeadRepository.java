package com.wexl.erp.fees.repository;

import com.wexl.erp.fees.dto.DueDetails;
import com.wexl.erp.fees.model.*;
import com.wexl.retail.model.Student;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface FeeHeadRepository extends JpaRepository<FeeHead, Long> {

  boolean existsByFeeMasterAndStudentAndFeeType(
      FeeMaster feeMaster, Student student, FeeType feeType);

  List<FeeHead> findAllByFeeMasterAndOrgSlug(FeeMaster feeMaster, String orgSlug);

  List<FeeHead> findAllByStudentIdAndOrgSlugAndStatusNotIn(
      Long studentId, String orgSlug, List<FeeStatus> status);

  Optional<FeeHead> findByIdAndOrgSlug(UUID uuid, String orgSlug);

  List<FeeHead> findAllByConcession(Concession concession);

  Long countByFeeTypeIn(List<FeeType> feeTypes);

  Optional<FeeHead> findByIdAndOrgSlugAndStudent(UUID id, String orgSlug, Student student);

  @Query(
      value =
          """
          SELECT fh.*
          FROM fee_heads fh
                     JOIN fee_masters fm ON fm.id = fh.fee_master_id
                     JOIN fee_groups fg ON fg.id = fm.fee_group_id
           WHERE fh.org_slug = :orgSlug
             AND fh.student_id IN (:studentIds)
             AND fh.status IN (0, 2)
             AND fg.is_active = TRUE
             AND fg.name IN (:feeNames)
             AND fh.due_date >= :fromDate
             AND fh.due_date <= :toDate
           ORDER BY fh.student_id, fh.due_date""",
      nativeQuery = true)
  List<FeeHead> findPastDueFeeDetails(
      @Param("orgSlug") String orgSlug,
      @Param("studentIds") List<Long> studentIds,
      @Param("feeNames") List<String> feeNames,
      @Param("fromDate") LocalDateTime fromDate,
      @Param("toDate") LocalDateTime toDate);

  @Query(
      value =
          """
          SELECT fh.*
          FROM fee_heads fh
                    JOIN fee_masters fm ON fm.id = fh.fee_master_id
                    JOIN fee_groups fg ON fg.id = fm.fee_group_id
          WHERE fh.org_slug = :orgSlug
            AND fh.student_id IN (:studentIds)
            AND fh.status IN (0,1,3)
            AND fg.is_active = TRUE
            AND fg.name IN (:feeNames)
            AND fh.due_date >= :fromDate
            AND fh.due_date <= :toDate
          ORDER BY fh.student_id, fh.due_date""",
      nativeQuery = true)
  List<FeeHead> findTotalDueFeeDetails(
      @Param("orgSlug") String orgSlug,
      @Param("studentIds") List<Long> studentIds,
      @Param("feeNames") List<String> feeNames,
      @Param("fromDate") LocalDateTime fromDate,
      @Param("toDate") LocalDateTime toDate);

  List<FeeHead> findAllByOrgSlug(String orgSlug);

  Optional<FeeHead> findByStudentAndFeeType(Student studentInfo, FeeType feeType);

  List<FeeHead> findAllByStudent(Student student);

  @Query(
      value =
          """
                  select fh.id as id,DATE(fh.due_date) as due_date,fh.balance_amount as due,fh.amount as total_fee,fh.fine_amount as fine,ft."name" as type_name from fee_heads fh
                  join fee_types ft on fh.fee_type_id = ft.id
                  where fh.org_slug = :orgSlug and student_id = :studentID and fh.due_date::date <= DATE(:date) and ft."name" != 'Admission Fee'
          """,
      nativeQuery = true)
  List<DueDetails> getByOrgSlugAndStudentIdAndDueDate(
      String orgSlug, Long studentID, LocalDate date);

  @Query(
      value =
          """

                                  select fh.* from fee_heads fh
                                  join fee_masters fm  on fm.id=fh.fee_master_id
                                  where fm.fee_group_id  = :feeGroupId and fh.org_slug = :orgSlug
                                  and fh.student_id = :studentId and fh.fee_type_id = :feeTypeId
                  """,
      nativeQuery = true)
  Optional<FeeHead> getFeeHeadByStudentIdAnFeeMasterIdAndFeeType(
      UUID feeGroupId, String orgSlug, Long studentId, UUID feeTypeId);

  @Query(
      value =
          """
          SELECT
              s.id as student_id,
              u.user_name as admission_number,
              CONCAT(u.first_name, ' ', u.last_name) as student_name,
              gf.first_name as father_name,
              gf.mobile_number as father_mobile,
              gm.first_name as mother_name,
              gm.mobile_number as mother_mobile,
              s.roll_number as roll_number,
              s.class_roll_number as class_roll_number,
              sec.name as class_name,
              u.gender as gender,
              'DAY SCHOLAR' as student_category,
              TO_CHAR(s.created_at, 'DD-MM-YYYY') as date_of_admission,
              CASE WHEN s.active = '1' THEN 'ACTIVE' ELSE 'INACTIVE' END as student_status,
              ft.name as fee_type_name,
              fh.amount as fee_amount,
              fh.fine_amount as fine_amount,
              fh.discount_amount as discount_amount,
              fh.paid_amount as paid_amount,
              fh.balance_amount as balance_amount,
              TO_CHAR(fp.created_at, 'YYYY-MM-DD') as payment_date,
              fp.reference_id as receipt_number,
              fpd.amount_paid as payment_amount,
              fm.academic_year as academic_year,
              fh.status as fee_status
          FROM students s
          INNER JOIN users u ON s.user_id = u.id
          INNER JOIN sections sec ON s.section_id = sec.id
          LEFT JOIN guardians gf ON gf.student_id = s.id AND gf.role = 'FATHER'
          LEFT JOIN guardians gm ON gm.student_id = s.id AND gm.role = 'MOTHER'
          LEFT JOIN fee_heads fh ON fh.student_id = s.id AND fh.org_slug = :orgSlug
          LEFT JOIN fee_masters fm ON fh.fee_master_id = fm.id
          LEFT JOIN fee_groups fg ON fm.fee_group_id = fg.id
          LEFT JOIN fee_types ft ON fh.fee_type_id = ft.id
          LEFT JOIN fee_payment_details fpd ON fpd.fee_head_id = fh.id
          LEFT JOIN fee_payments fp ON fpd.fee_payment_id = fp.id
          WHERE u.organization = :orgSlug
            AND u.deleted_at IS NULL
            AND (:academicYear IS NULL OR fm.academic_year = :academicYear)
            AND (:sectionUuids IS NULL OR sec.uuid::text = ANY(CAST(:sectionUuids AS text[])))
          ORDER BY s.id, ft.name, fp.created_at
          """,
      nativeQuery = true)
  List<Object[]> findFeeHeadMasterReportData(
      @Param("orgSlug") String orgSlug,
      @Param("academicYear") String academicYear,
      @Param("sectionUuids") List<String> sectionUuids);

  @Query(
      value =
          """
          SELECT
              CONCAT(u.first_name, ' ', u.last_name) as student_name,
              u.user_name as student_registration_id,
              sec.name as class_name,
              gf.first_name as father_name,
              s.class_roll_number as class_roll_number,
              CASE WHEN s.active = '1' THEN 'active' ELSE 'inactive' END as student_status,
              ft.name as fee_head,
              'Term 1' as term,
              fh.amount as amount,
              fh.due_date as due_date
          FROM students s
          INNER JOIN users u ON s.user_id = u.id
          INNER JOIN sections sec ON s.section_id = sec.id
          LEFT JOIN guardians gf ON gf.student_id = s.id AND gf.role = 'FATHER'
          INNER JOIN fee_heads fh ON fh.student_id = s.id AND fh.org_slug = :orgSlug
          INNER JOIN fee_masters fm ON fh.fee_master_id = fm.id
          INNER JOIN fee_types ft ON fh.fee_type_id = ft.id
          WHERE u.organization = :orgSlug
            AND u.deleted_at IS NULL
            AND (:academicYear IS NULL OR fm.academic_year = :academicYear)
            AND (:term IS NULL OR ft.name LIKE CONCAT('%', :term, '%'))
            AND (:studentFilter IS NULL OR CONCAT(u.first_name, ' ', u.last_name) ILIKE CONCAT('%', :studentFilter, '%'))
          ORDER BY student_name, ft.name
          """,
      nativeQuery = true)
  List<Object[]> findStudentTermWiseReportData(
      @Param("orgSlug") String orgSlug,
      @Param("academicYear") String academicYear,
      @Param("term") String term,
      @Param("studentFilter") String studentFilter);
}
