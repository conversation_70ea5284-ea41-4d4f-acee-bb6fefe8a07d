package com.wexl.erp.fees.config;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import lombok.Getter;
import org.springframework.stereotype.Component;

@Component
@Getter
public class FeeReportConfig {

  // Fee Head Master Report Configuration
  public static class FeeHeadMasterReportConfig {
    public static final List<String> STATIC_HEADERS = Arrays.asList(
        "Sno", "Admission Number", "Name of Student", "Father Name", "Father Mobile No",
        "Mother Name", "Roll No", "Mother Mobile No", "Class", "Gender", "Student Category",
        "Date of Admission", "Student Status");

    public static final List<String> SUMMARY_HEADERS = Arrays.asList(
        "Total", "Discounts", "Grand Total");

    public static final List<String> COLLECTION_SUFFIX_HEADERS = Arrays.asList(
        "Total Collected", "Due");

    // Field mappings for dynamic data extraction
    public static final Map<String, Function<Object[], Object>> FIELD_MAPPINGS = Map.of(
        "student_id", row -> row[0],
        "admission_number", row -> row[1],
        "student_name", row -> row[2],
        "father_name", row -> row[3],
        "father_mobile", row -> row[4],
        "mother_name", row -> row[5],
        "mother_mobile", row -> row[6],
        "roll_number", row -> row[7],
        "class_roll_number", row -> row[8],
        "class_name", row -> row[9],
        "gender", row -> row[10],
        "student_category", row -> row[11],
        "date_of_admission", row -> row[12],
        "student_status", row -> row[13],
        "fee_type_name", row -> row[15],
        "fee_group_name", row -> row[16],
        "fee_amount", row -> row[17],
        "fine_amount", row -> row[18],
        "discount_amount", row -> row[19],
        "paid_amount", row -> row[21],
        "balance_amount", row -> row[22],
        "payment_date", row -> row[25],
        "receipt_number", row -> row[26]
    );
  }

  // Student Term-wise Report Configuration
  public static class StudentTermWiseReportConfig {
    public static final List<String> HEADERS = Arrays.asList(
        "Student Name", "Student Registration Id", "Class", "Father Name",
        "Class Roll Number", "Student Status", "Fee Head", "Term", "Amount", "Due Date");

    // Field mappings for data extraction
    public static final Map<String, Function<Object[], Object>> FIELD_MAPPINGS = Map.of(
        "student_name", row -> row[0],
        "student_registration_id", row -> row[1],
        "class_name", row -> row[2],
        "father_name", row -> row[3],
        "class_roll_number", row -> row[4],
        "student_status", row -> row[5],
        "fee_head", row -> row[6],
        "term", row -> row[7],
        "amount", row -> row[8],
        "due_date", row -> row[9]
    );
  }

  // Report Type Enumeration
  public enum ReportType {
    FEE_HEAD_MASTER("fee_head_master", "Fee Head Master Report"),
    STUDENT_TERM_WISE("student_term_wise", "Student Term-wise Report");

    private final String code;
    private final String displayName;

    ReportType(String code, String displayName) {
      this.code = code;
      this.displayName = displayName;
    }

    public String getCode() {
      return code;
    }

    public String getDisplayName() {
      return displayName;
    }

    public static ReportType fromCode(String code) {
      for (ReportType type : values()) {
        if (type.code.equals(code)) {
          return type;
        }
      }
      throw new IllegalArgumentException("Unknown report type: " + code);
    }
  }

  // Fee Type Categories for dynamic header generation
  public static class FeeTypeCategories {
    public static final List<String> STANDARD_FEE_TYPES = Arrays.asList(
        "Admission Fee", "Tuition Fee", "Transport Fee", "Late Fee", 
        "Extended Day Care", "Library Fee", "Sports Fee", "Activity Fee");

    public static final List<String> LATE_FEE_TYPES = Arrays.asList(
        "Tuition Fee - LateFee", "Transport Fee - LateFee", "Late Fee - LateFee",
        "Extended Day Care - LateFee");

    // Priority order for fee type display
    public static final Map<String, Integer> FEE_TYPE_PRIORITY = Map.of(
        "Admission Fee", 1,
        "Tuition Fee", 2,
        "Transport Fee", 3,
        "Late Fee", 4,
        "Extended Day Care", 5,
        "Library Fee", 6,
        "Sports Fee", 7,
        "Activity Fee", 8,
        "Tuition Fee - LateFee", 9,
        "Transport Fee - LateFee", 10,
        "Late Fee - LateFee", 11,
        "Extended Day Care - LateFee", 12
    );
  }

  // Data formatting configurations
  public static class FormatConfig {
    public static final String DATE_FORMAT = "dd-MM-yyyy";
    public static final String AMOUNT_FORMAT = "%.2f";
    public static final String DEFAULT_CURRENCY = "INR";
    
    // Default values for missing data
    public static final String DEFAULT_STRING_VALUE = "";
    public static final Double DEFAULT_AMOUNT_VALUE = 0.0;
    public static final String DEFAULT_STATUS = "ACTIVE";
    public static final String DEFAULT_CATEGORY = "DAY SCHOLAR";
  }

  // File naming configurations
  public static class FileNameConfig {
    public static final String FEE_HEAD_MASTER_FILENAME = "Fee_Head_Master_Report";
    public static final String STUDENT_TERM_WISE_FILENAME = "Student_Term_Wise_Report";
    public static final String FILE_EXTENSION = ".csv";
    
    public static String generateFileName(ReportType reportType, String academicYear) {
      String baseName = reportType == ReportType.FEE_HEAD_MASTER 
          ? FEE_HEAD_MASTER_FILENAME 
          : STUDENT_TERM_WISE_FILENAME;
      
      if (academicYear != null && !academicYear.isEmpty()) {
        baseName += "_" + academicYear;
      }
      
      return baseName + FILE_EXTENSION;
    }
  }

  // Validation configurations
  public static class ValidationConfig {
    public static final List<String> REQUIRED_FIELDS_FEE_HEAD_MASTER = Arrays.asList(
        "student_name", "admission_number", "class_name");
    
    public static final List<String> REQUIRED_FIELDS_STUDENT_TERM_WISE = Arrays.asList(
        "student_name", "student_registration_id", "class_name", "fee_head");
    
    public static final int MAX_RECORDS_PER_REPORT = 10000;
    public static final int MIN_RECORDS_FOR_REPORT = 1;
  }

  // CSV generation configurations
  public static class CsvConfig {
    public static final String CHARSET = "UTF-8";
    public static final String CONTENT_TYPE = "text/csv; charset=UTF-8";
    public static final String HEADER_CONTENT_DISPOSITION = "Content-Disposition";
    public static final String ATTACHMENT_PREFIX = "attachment; filename=\"";
    public static final String ATTACHMENT_SUFFIX = "\"";
  }
}
