package com.wexl.erp.paymentGateway.types;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.paymentGateway.dto.PaymentGatewayDto;
import com.wexl.erp.paymentGateway.dto.PaymentMethod;
import com.wexl.erp.paymentGateway.model.PaymentGatewayDetail;
import com.wexl.erp.paymentGateway.repository.PaymentGatewayDetailRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Component
@RequiredArgsConstructor
public class EaseBuzz implements PaymentGatewayRule {

  private final PaymentGatewayDetailRepository paymentGatewayDetailRepository;

  @Value("${app.publicUrl}")
  private String publicUrl;

  @Value("${app.easeBuzzInitiateUrl}")
  private String easeBuzzInitiateUrl;

  @Override
  public boolean supports(PaymentMethod paymentMethod) {
    return PaymentMethod.EASEBUZZ_PAYMENT.equals(paymentMethod);
  }

  @Override
  public PaymentGatewayDto.Response initiatePayment(
      String orgSlug,
      FeeDto.CollectFeeRequest request,
      FeeHead feeHead,
      PaymentGatewayDetail gatewayDetail) {

    try {
      String txnId = generateTxnId();
      JSONObject payload = buildOrderPayload(request, feeHead, gatewayDetail, txnId);
      String responseBody = callApi(easeBuzzInitiateUrl, payload);

      log.info("Easebuzz Raw Response: {}", responseBody);

      org.json.JSONObject res = new org.json.JSONObject(responseBody);
      if (!"1".equalsIgnoreCase(res.optString("status"))) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST,
            "Easebuzz initiate payment failed: " + res.optString("data"));
      }

      return PaymentGatewayDto.Response.builder()
          .referenceId(res.get("data").toString())
          .jsonObject(res)
          .txnId(txnId)
          .hash(payload.getAsString("hash"))
          .build();

    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Failed to create Easebuzz order", e);
    }
  }

  private JSONObject buildOrderPayload(
      FeeDto.CollectFeeRequest request,
      FeeHead feeHead,
      PaymentGatewayDetail gatewayDetail,
      String txnId) {

    String key = gatewayDetail.getConfig().getKeyId();
    String salt = gatewayDetail.getConfig().getSecretKey();

    String amount = request.amount().toString();
    String productInfo = "fee";
    String firstname = feeHead.getStudent().getUserInfo().getFirstName();
    String email = feeHead.getStudent().getUserInfo().getEmail();
    String phone = sanitizePhone(feeHead.getStudent().getUserInfo().getMobileNumber());

    // Correct Easebuzz hash sequence
    String easeBuzzHashString =
        key
            + "|"
            + txnId
            + "|"
            + amount
            + "|"
            + productInfo
            + "|"
            + firstname
            + "|"
            + email
            + "|||||||||||"
            + salt;

    String hash = DigestUtils.sha512Hex(easeBuzzHashString.getBytes(StandardCharsets.UTF_8));

    JSONObject json = new JSONObject();
    json.put("key", key);
    json.put("txnid", txnId);
    json.put("amount", amount);
    json.put("firstname", firstname);
    json.put("email", email);
    json.put("phone", phone);
    json.put("productinfo", productInfo);
    json.put("hash", hash);
    json.put("surl", getSuccessUrl(feeHead.getOrgSlug(), txnId));
    json.put("furl", getFailureUrl(feeHead.getOrgSlug(), txnId));
    return json;
  }

  private String callApi(String url, JSONObject payload) throws IOException {
    RestTemplate restTemplate = new RestTemplate();

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

    // Convert JSON → form-urlencoded with URL encoding
    StringBuilder bodyBuilder = new StringBuilder();
    payload.forEach(
        (k, v) -> {
          if (!bodyBuilder.isEmpty()) bodyBuilder.append("&");
          bodyBuilder
              .append(URLEncoder.encode(k, StandardCharsets.UTF_8))
              .append("=")
              .append(URLEncoder.encode(String.valueOf(v), StandardCharsets.UTF_8));
        });

    log.info("Easebuzz Payload (form): {}", bodyBuilder);

    HttpEntity<String> entity = new HttpEntity<>(bodyBuilder.toString(), headers);

    ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

    if (!response.getStatusCode().is2xxSuccessful()) {
      throw new IOException("Unexpected: " + response.getStatusCodeValue());
    }

    return Objects.requireNonNull(response.getBody(), "Response body is null");
  }

  private String sanitizePhone(String rawPhone) {
    String phone = rawPhone.replaceAll("\\D", "");
    if (phone.startsWith("91") && phone.length() > 10) {
      phone = phone.substring(phone.length() - 10);
    }
    return phone;
  }

  public static String generateTxnId() {
    return "TXN" + Long.toHexString(System.currentTimeMillis()).toUpperCase();
  }

  private String getSuccessUrl(String orgSlug, String txnId) {
    return String.format(publicUrl + "%s/easebuzz-payment/success?txnid=%s", orgSlug, txnId);
  }

  private String getFailureUrl(String orgSlug, String txnId) {
    return String.format(publicUrl + "%s/easebuzz-payment/failure?txnid=%s", orgSlug, txnId);
  }

  @Override
  public void verifyPayment(
      String orgSlug,
      String paymentId,
      PaymentGatewayDto.VerifyPaymentRequest request,
      PaymentGatewayDetail gatewayDetail) {}

  public boolean verifyHash(String orgSlug, PaymentGatewayDto.CallBackRequest params) {
    try {
      var config =
          paymentGatewayDetailRepository.findByOrgSlugAndPaymentMethod(
              orgSlug, PaymentMethod.EASEBUZZ_PAYMENT);
      String salt = config.getConfig().getSecretKey();
      String status = params.status();

      // Hash sequence for response verification:
      String hashString =
          salt
              + "|"
              + status
              + "|"
              + params.email()
              + "|"
              + params.firstName()
              + "|"
              + params.productinfo()
              + "|"
              + params.amount()
              + "|"
              + params.txnId()
              + "|"
              + config.getConfig().getKeyId();

      String expectedHash = DigestUtils.sha512Hex(hashString.getBytes(StandardCharsets.UTF_8));
      return expectedHash.equalsIgnoreCase(params.hash());
    } catch (Exception e) {
      log.error("Error while verifying hash", e);
      return false;
    }
  }
}
