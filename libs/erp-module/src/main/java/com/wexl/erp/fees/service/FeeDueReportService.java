package com.wexl.erp.fees.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.model.Student;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.CsvUtils;
import jakarta.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FeeDueReportService {

  private final FeeHeadRepository feeHeadRepository;
  private final StudentRepository studentRepository;
  private final SectionRepository sectionRepository;
  private final UserService userService;
  private final DateTimeUtil dateTimeUtil;
  private final GuardianService guardianService;

  private static final List<String> CSV_HEADERS =
      Arrays.asList(
          "STUDENT NAME",
          "FATHER NAME",
          "MOTHER NAME",
          "GUARDIAN NAME",
          "MOBILE NUMBER",
          "ADMISSION NUMBER",
          "ROLL NUMBER",
          "SECTION NAME",
          "DATE OF ADMISSION");

  private static final List<String> FEE_HEAD_MASTER_HEADERS = Arrays.asList(
          "Sno", "Admission Number", "Name of Student", "Father Name", "Father Mobile No",
          "Mother Name", "Roll No", "Mother Mobile No", "Class", "Gender", "Student Category",
          "Date of Admission", "Student Status");

  private static final List<String> STUDENT_TERM_WISE_HEADERS = Arrays.asList(
          "Student Name", "Student Registration Id", "Class", "Father Name",
          "Class Roll Number", "Student Status", "Fee Head", "Term", "Amount", "Due Date");

  public void generateFeeDueReportCsv(
      String orgSlug, FeeDto.FeeDueReportRequest request, HttpServletResponse response) {
    List<FeeHead> currentFeeHeads = new ArrayList<>();

    List<FeeDto.FeeDueReportResponse> reportData =
        generateFeeDueReport(orgSlug, request, currentFeeHeads);
    String reportType = request.reportType() != null ? request.reportType() : "total_due";
    generateCsvResponse(reportData, response, reportType, request, currentFeeHeads);
  }

  public List<FeeDto.FeeDueReportResponse> generateFeeDueReport(
      String orgSlug, FeeDto.FeeDueReportRequest request, List<FeeHead> currentFeeHeads) {
    List<Student> students = getStudentsBySectionUuids(request);
    List<FeeHead> feeHeads = getFeeHeadsByReportType(orgSlug, students, request);

    currentFeeHeads.addAll(feeHeads);
    Map<Student, List<FeeHead>> feeHeadsByStudent =
        feeHeads.stream().collect(Collectors.groupingBy(FeeHead::getStudent));

    String reportType = request.reportType() != null ? request.reportType() : "total_due";
    return students.stream()
        .map(
            student -> {
              List<FeeHead> studentFeeHeads = feeHeadsByStudent.get(student);
              if ("past_due".equals(reportType)) {
                if (studentFeeHeads == null
                    || studentFeeHeads.stream()
                            .map(FeeHead::getBalanceAmount)
                            .filter(Objects::nonNull)
                            .mapToDouble(Double::doubleValue)
                            .sum()
                        <= 0) {
                  return null;
                }
              }
              Double discount = calculateDiscount(student);
              return buildFeeDueReportResponse(discount, student, studentFeeHeads);
            })
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  private Double calculateDiscount(Student student) {
    var feeHeadList = feeHeadRepository.findAllByStudent(student);
    return (feeHeadList == null || feeHeadList.isEmpty())
        ? 0.0
        : feeHeadList.stream()
            .map(FeeHead::getDiscountAmount)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();
  }

  public List<Student> getStudentsBySectionUuids(FeeDto.FeeDueReportRequest request) {
    List<Section> sections =
        sectionRepository.findAllByUuidIn(
            request.sectionUuids().stream().map(UUID::fromString).toList());
    List<Student> students = new ArrayList<>();
    for (Section section : sections) {
      List<Student> sectionStudents = studentRepository.getStudentsBySection(section);
      students.addAll(sectionStudents);
    }
    return students;
  }

  private List<FeeHead> getFeeHeadsByReportType(
      String orgSlug, List<Student> students, FeeDto.FeeDueReportRequest request) {

    List<Long> studentIds = students.stream().map(Student::getId).collect(Collectors.toList());
    List<String> feeNames = request.feeGroupTypes();

    String reportType =
        request.reportType() != null ? request.reportType().toLowerCase() : "total_due";

    return switch (reportType) {
      case "past_due" ->
          feeHeadRepository.findPastDueFeeDetails(
              orgSlug,
              studentIds,
              feeNames,
              dateTimeUtil.convertEpochToIso8601(request.fromDate()),
              dateTimeUtil.convertEpochToIso8601(request.toDate()));
      case "total_due" ->
          feeHeadRepository.findTotalDueFeeDetails(
              orgSlug,
              studentIds,
              feeNames,
              dateTimeUtil.convertEpochToIso8601(request.fromDate()),
              dateTimeUtil.convertEpochToIso8601(request.toDate()));
      default -> Collections.emptyList();
    };
  }

  private FeeDto.FeeDueReportResponse buildFeeDueReportResponse(
      Double discount, Student student, List<FeeHead> feeHeads) {
    if (feeHeads == null || feeHeads.isEmpty()) {
      return null;
    }

    List<FeeDto.FeeDetailResponse> feeDetails =
        feeHeads.stream().map(this::buildFeeDetailResponse).collect(Collectors.toList());

    Double totalDueAmount =
        feeHeads.stream()
            .mapToDouble(
                feeHead -> feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
            .sum();

    return FeeDto.FeeDueReportResponse.builder()
        .studentName(userService.getNameByUserInfo(student.getUserInfo()))
        .fatherName(
            student.getGuardians().stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.FATHER))
                .findAny()
                .map(guardianService::getGuardianName)
                .orElse(null))
        .motherName(
            student.getGuardians().stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.MOTHER))
                .findAny()
                .map(guardianService::getGuardianName)
                .orElse(null))
        .guardianName(
            student.getGuardians().stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.GUARDIAN))
                .findAny()
                .map(guardianService::getGuardianName)
                .orElse(null))
        .mobileNumber(student.getUserInfo().getMobileNumber())
        .admissionNumber(student.getUserInfo().getUserName())
        .rollNumber(student.getClassRollNumber())
        .sectionName(student.getSection() != null ? student.getSection().getName() : "")
        .dateOfAdmission(
            student.getCreatedAt() != null
                ? student
                    .getCreatedAt()
                    .toLocalDateTime()
                    .format(DateTimeFormatter.ofPattern("dd-MM-yyyy"))
                : "")
        .feeDetails(feeDetails)
        .discountAmount(discount)
        .totalDueAmount(totalDueAmount)
        .build();
  }

  private FeeDto.FeeDetailResponse buildFeeDetailResponse(FeeHead feeHead) {
    return FeeDto.FeeDetailResponse.builder()
        .feeTypeName(feeHead.getFeeType().getName())
        .month(
            feeHead.getDueDate() != null
                ? feeHead.getDueDate().format(DateTimeFormatter.ofPattern("MMM")).toUpperCase()
                : "")
        .amount(feeHead.getAmount())
        .paidAmount(feeHead.getPaidAmount() != null ? feeHead.getPaidAmount() : 0.0)
        .balanceAmount(feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
        .dueDate(convertIso8601ToEpoch(feeHead.getDueDate()))
        .status(feeHead.getStatus())
        .build();
  }

  private void generateCsvResponse(
      List<FeeDto.FeeDueReportResponse> reportData,
      HttpServletResponse response,
      String reportType,
      FeeDto.FeeDueReportRequest request,
      List<FeeHead> currentFeeHeads) {

    String fileName = getFileName(reportType);
    response.setContentType("text/csv; charset=UTF-8");
    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

    List<List<String>> csvData = buildCsvBody(reportData, request, currentFeeHeads);
    if (csvData.isEmpty()) {
      CsvUtils.generateCsv(CSV_HEADERS.toArray(new String[0]), new ArrayList<>(), response);
      return;
    }

    List<String> csvHeaders = csvData.removeFirst();
    CsvUtils.generateCsv(csvHeaders.toArray(new String[0]), csvData, response);
  }

  private String getFileName(String reportType) {
    String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm_dd_MM_yyyy"));
    return switch (reportType) {
      case "past_due" -> "past_due_report_" + timestamp + ".csv";
      case "total_due" -> "total_due_report_" + timestamp + ".csv";
      default -> "fee_due_report_" + timestamp + ".csv";
    };
  }

  private List<List<String>> buildCsvBody(
      List<FeeDto.FeeDueReportResponse> reportData,
      FeeDto.FeeDueReportRequest request,
      List<FeeHead> currentFeeHeads) {
    List<List<String>> csvBody = new ArrayList<>();

    if (reportData.isEmpty()) {
      return csvBody;
    }

    List<String> headers = generateDynamicHeaders(request, currentFeeHeads);
    csvBody.add(headers);

    for (FeeDto.FeeDueReportResponse report : reportData) {
      List<String> row = buildDynamicDataRow(report, request, currentFeeHeads);
      csvBody.add(row);
    }

    return csvBody;
  }

  private List<String> generateDynamicHeaders(
      FeeDto.FeeDueReportRequest request, List<FeeHead> currentFeeHeads) {

    List<String> headers = new ArrayList<>(CSV_HEADERS);
    Map<String, Set<String>> feeGroupStructure = buildFeeGroupStructure(request, currentFeeHeads);

    for (Map.Entry<String, Set<String>> entry : feeGroupStructure.entrySet()) {
      String feeGroupDescription = entry.getKey();
      Set<String> feeTypes = entry.getValue();

      if (feeTypes.isEmpty()) {
        headers.add(feeGroupDescription);
      } else {
        for (String feeType : feeTypes) {
          headers.add(feeGroupDescription + " " + feeType);
        }
      }
    }

    headers.add("DISCOUNT AMOUNT");
    headers.add("TOTAL DUE AMOUNT");
    //    headers.addAll(List.of("Delete", "EXTENDED DAY", "Fee Remark"));

    return headers;
  }

  private Map<String, Set<String>> buildFeeGroupStructure(
      FeeDto.FeeDueReportRequest request, List<FeeHead> currentFeeHeads) {
    Map<String, Set<String>> feeGroupStructure = new LinkedHashMap<>();

    if (currentFeeHeads != null) {
      for (FeeHead feeHead : currentFeeHeads) {
        if (feeHead.getFeeMaster() != null
            && feeHead.getFeeMaster().getFeeGroup() != null
            && feeHead.getFeeType() != null) {
          String feeGroupDescription = feeHead.getFeeMaster().getFeeGroup().getName();
          String feeTypeName = feeHead.getFeeType().getName();

          feeGroupStructure
              .computeIfAbsent(feeGroupDescription, k -> new LinkedHashSet<>())
              .add(feeTypeName);
        }
      }
    }

    List<String> requestedFeeGroupTypes = request.feeGroupTypes();
    if (requestedFeeGroupTypes != null && !requestedFeeGroupTypes.isEmpty()) {
      Map<String, Set<String>> filteredStructure = new LinkedHashMap<>();
      for (String requestedType : requestedFeeGroupTypes) {
        if (feeGroupStructure.containsKey(requestedType)) {
          filteredStructure.put(requestedType, feeGroupStructure.get(requestedType));
        }
      }
      return filteredStructure;
    }

    return feeGroupStructure;
  }

  private String getFeeGroupDescriptionForStudent(
      Long studentId, String feeTypeName, List<FeeHead> currentFeeHeads) {
    if (currentFeeHeads == null || studentId == null || feeTypeName == null) {
      return "Unknown Fee Group";
    }

    for (FeeHead feeHead : currentFeeHeads) {
      if (feeHead.getStudent() != null
          && feeHead.getStudent().getId() == (studentId)
          && feeHead.getFeeType() != null
          && feeTypeName.equals(feeHead.getFeeType().getName())
          && feeHead.getFeeMaster() != null
          && feeHead.getFeeMaster().getFeeGroup() != null) {
        return feeHead.getFeeMaster().getFeeGroup().getName();
      }
    }
    return "Unknown Fee Group";
  }

  private List<String> buildDynamicDataRow(
      FeeDto.FeeDueReportResponse report,
      FeeDto.FeeDueReportRequest request,
      List<FeeHead> currentFeeHeads) {
    List<String> row = new ArrayList<>();

    row.add(report.studentName() != null ? report.studentName() : "");
    row.add(report.fatherName() != null ? report.fatherName() : "");
    row.add(report.motherName() != null ? report.motherName() : "");
    row.add(report.guardianName() != null ? report.guardianName() : "");
    row.add(report.mobileNumber() != null ? report.mobileNumber() : "");
    row.add(report.admissionNumber() != null ? report.admissionNumber() : "");
    row.add(report.rollNumber() != null ? report.rollNumber() : "");
    row.add(report.sectionName() != null ? report.sectionName() : "");
    row.add(report.dateOfAdmission() != null ? report.dateOfAdmission() : "");

    Map<String, Map<String, Double>> feeGroupAmounts =
        buildFeeGroupAmountsMap(report, currentFeeHeads);
    Map<String, Set<String>> feeGroupStructure = buildFeeGroupStructure(request, currentFeeHeads);

    for (Map.Entry<String, Set<String>> entry : feeGroupStructure.entrySet()) {
      String feeGroupDescription = entry.getKey();
      Set<String> feeTypes = entry.getValue();

      if (feeTypes.isEmpty()) {
        Double amount =
            feeGroupAmounts.getOrDefault(feeGroupDescription, new HashMap<>()).values().stream()
                .mapToDouble(Double::doubleValue)
                .sum();
        row.add(String.format("%.0f", amount));
      } else {
        for (String feeType : feeTypes) {
          Double amount =
              feeGroupAmounts
                  .getOrDefault(feeGroupDescription, new HashMap<>())
                  .getOrDefault(feeType, 0.0);
          row.add(String.format("%.0f", amount));
        }
      }
    }

    row.add(String.format("%.0f", report.discountAmount()));
    row.add(String.format("%.0f", report.totalDueAmount()));
    //    row.add("0");
    //    row.add("0");
    //    row.add("");

    return row;
  }

  private Map<String, Map<String, Double>> buildFeeGroupAmountsMap(
      FeeDto.FeeDueReportResponse report, List<FeeHead> currentFeeHeads) {
    Map<String, Map<String, Double>> feeGroupAmounts = new HashMap<>();

    Long studentId = getStudentIdFromReport(report, currentFeeHeads);

    for (FeeDto.FeeDetailResponse detail : report.feeDetails()) {
      String feeGroupDescription =
          getFeeGroupDescriptionForStudent(studentId, detail.feeTypeName(), currentFeeHeads);
      String feeTypeName = detail.feeTypeName();
      Double amount = detail.balanceAmount() != null ? detail.balanceAmount() : 0.0;

      feeGroupAmounts
          .computeIfAbsent(feeGroupDescription, k -> new HashMap<>())
          .merge(feeTypeName, amount, Double::sum);
    }

    return feeGroupAmounts;
  }

  private Long getStudentIdFromReport(
      FeeDto.FeeDueReportResponse report, List<FeeHead> currentFeeHeads) {
    String admissionNumber = report.admissionNumber();
    if (admissionNumber == null) {
      return null;
    }

    for (FeeHead feeHead : currentFeeHeads) {
      if (feeHead.getStudent() != null
          && feeHead.getStudent().getUserInfo() != null
          && admissionNumber.equals(feeHead.getStudent().getUserInfo().getUserName())) {
        return feeHead.getStudent().getId();
      }
    }
    return null;
  }
  public void generateFeeHeadMasterAndStudentTermWiseReportCsv(
          String orgSlug, FeeDto.FeeReportRequest request, HttpServletResponse response) {

    String reportType = request.reportType();
    if ("fee_head_master".equals(reportType)) {
      generateFeeHeadMasterReportCsv(orgSlug, request, response);
    } else if ("student_term_wise".equals(reportType)) {
      generateStudentTermWiseReportCsv(orgSlug, request, response);
    } else {
      throw new IllegalArgumentException("Invalid report type: " + reportType);
    }
  }

  private void generateFeeHeadMasterReportCsv(
          String orgSlug, FeeDto.FeeReportRequest request, HttpServletResponse response) {

    List<Object[]> rawData = feeHeadRepository.findFeeHeadMasterReportData(orgSlug, request.academicYear(), request.sectionUuids());

    List<FeeDto.FeeReportResponse> reportData = processFeeHeadMasterData(rawData);
    List<String> dynamicHeaders = generateFeeHeadMasterHeaders(reportData);
    List<List<String>> csvData = buildFeeHeadMasterCsvData(reportData, dynamicHeaders);

    generateCsvResponse(csvData, dynamicHeaders, "Fee_Head_Master_Report"+ DateTimeFormatter.ofPattern("yyyy-MM-dd_HH:mm:ss").format(LocalDateTime.now())+".csv", response);
  }

  private void generateStudentTermWiseReportCsv(
          String orgSlug, FeeDto.FeeReportRequest request, HttpServletResponse response) {

    List<Object[]> rawData = feeHeadRepository.findStudentTermWiseReportData(
            orgSlug, request.academicYear(), request.term(),
            request.studentFilter());

    List<FeeDto.FeeReportResponse> reportData = processStudentTermWiseData(rawData);

    List<List<String>> csvData = buildStudentTermWiseCsvData(reportData);

    generateCsvResponse(csvData, STUDENT_TERM_WISE_HEADERS, "Student_Term_Wise_Report"+  DateTimeFormatter.ofPattern("yyyy-MM-dd_HH:mm:ss").format(LocalDateTime.now())+".csv", response);
  }

  private List<FeeDto.FeeReportResponse> processFeeHeadMasterData(List<Object[]> rawData) {
    Map<Long, FeeDto.FeeReportResponse.FeeReportResponseBuilder> studentDataMap = new HashMap<>();
    Map<Long, Map<String, Double>> feeApplicableMap = new HashMap<>();
    Map<Long, Map<String, FeeDto.FeeCollectionDetail>> feeCollectedMap = new HashMap<>();

    int sno = 1;
    for (Object[] row : rawData) {
      Long studentId = ((Number) row[0]).longValue();

      if (!studentDataMap.containsKey(studentId)) {
        FeeDto.FeeReportResponse.FeeReportResponseBuilder builder = FeeDto.FeeReportResponse.builder()
                .sno(sno++)
                .admissionNumber((String) row[1])
                .studentName((String) row[2])
                .fatherName((String) row[3])
                .fatherMobile((String) row[4])
                .motherName((String) row[5])
                .motherMobile((String) row[6])
                .rollNumber((String) row[7])
                .className((String) row[9])
                .gender((String) row[10])
                .studentCategory((String) row[11])
                .dateOfAdmission(formatDate(row[12]))
                .studentStatus((String) row[13]);

        studentDataMap.put(studentId, builder);
        feeApplicableMap.put(studentId, new HashMap<>());
        feeCollectedMap.put(studentId, new HashMap<>());
      }

      if (row[15] != null) {
        String feeTypeName = (String) row[15];
        Double feeAmount = row[16] != null ? ((Number) row[16]).doubleValue() : 0.0;
        Double paidAmount = row[17] != null ? ((Number) row[17]).doubleValue() : 0.0;

        feeApplicableMap.get(studentId).put(feeTypeName, feeAmount);

        if (paidAmount > 0) {
          String paymentDate = formatDate(row[18]);
          String receiptNumber = (String) row[19];

          feeCollectedMap.get(studentId).put(feeTypeName,
                  FeeDto.FeeCollectionDetail.builder()
                          .amount(paidAmount)
                          .date(paymentDate)
                          .receiptNumber(receiptNumber)
                          .build());
        }
      }
    }

    return studentDataMap.entrySet().stream()
            .map(entry -> {
              Long studentId = entry.getKey();
              FeeDto.FeeReportResponse.FeeReportResponseBuilder builder = entry.getValue();

              Map<String, Double> feeApplicable = feeApplicableMap.get(studentId);
              Map<String, FeeDto.FeeCollectionDetail> feeCollected = feeCollectedMap.get(studentId);

              Double grandTotal = feeApplicable.values().stream()
                      .mapToDouble(Double::doubleValue).sum();
              Double totalCollected = feeCollected.values().stream()
                      .mapToDouble(FeeDto.FeeCollectionDetail::amount).sum();
              Double dueAmount = grandTotal - totalCollected;

              return builder
                      .feeApplicable(feeApplicable)
                      .feeCollected(feeCollected)
                      .grandTotal(grandTotal)
                      .totalCollected(totalCollected)
                      .dueAmount(dueAmount)
                      .discounts(0.0)
                      .build();
            })
            .collect(Collectors.toList());
  }

  private List<FeeDto.FeeReportResponse> processStudentTermWiseData(List<Object[]> rawData) {
    return rawData.stream()
            .map(row -> FeeDto.FeeReportResponse.builder()
                    .studentName((String) row[0])
                    .admissionNumber((String) row[1])
                    .className((String) row[2])
                    .fatherName((String) row[3])
                    .rollNumber((String) row[4])
                    .studentStatus((String) row[5])
                    .feeHead((String) row[6])
                    .term((String) row[7])
                    .amount(row[8] != null ? ((Number) row[8]).doubleValue() : 0.0)
                    .dueDate(formatDate(row[9]))
                    .build())
            .collect(Collectors.toList());
  }

  private List<String> generateFeeHeadMasterHeaders(List<FeeDto.FeeReportResponse> reportData) {
    List<String> headers = new ArrayList<>(FEE_HEAD_MASTER_HEADERS);

    Set<String> feeTypes = reportData.stream()
            .flatMap(response -> response.feeApplicable().keySet().stream())
            .collect(Collectors.toSet());

    headers.addAll(feeTypes);

    headers.add("Total");
    headers.add("Discounts");
    headers.add("Grand Total");

    feeTypes.forEach(feeType -> headers.add(feeType + " (Collected)"));

    headers.add("Total Collected");
    headers.add("Due");

    return headers;
  }

  private String formatAmount(Double amount) {
    return amount != null ? String.format("%.2f", amount) : "0.0";
  }

  private List<List<String>> buildFeeHeadMasterCsvData(
          List<FeeDto.FeeReportResponse> reportData, List<String> headers) {

    List<List<String>> csvData = new ArrayList<>();

    for (FeeDto.FeeReportResponse response : reportData) {
      List<String> row = new ArrayList<>();

      row.add(String.valueOf(response.sno()));
      row.add(response.admissionNumber());
      row.add(response.studentName());
      row.add(response.fatherName());
      row.add(response.fatherMobile());
      row.add(response.motherName());
      row.add(response.rollNumber());
      row.add(response.motherMobile());
      row.add(response.className());
      row.add(response.gender());
      row.add(response.studentCategory());
      row.add(response.dateOfAdmission());
      row.add(response.studentStatus());

      Set<String> feeTypes = extractFeeTypesFromHeaders(headers);
      for (String feeType : feeTypes) {
        Double amount = response.feeApplicable().getOrDefault(feeType, 0.0);
        row.add(formatAmount(amount));
      }

      row.add(formatAmount(response.grandTotal()));
      row.add(formatAmount(response.discounts()));
      row.add(formatAmount(response.grandTotal() - response.discounts()));

      for (String feeType : feeTypes) {
        FeeDto.FeeCollectionDetail detail = response.feeCollected().get(feeType);
        row.add(detail != null ? formatAmount(detail.amount()) : "0.0");
      }

      row.add(formatAmount(response.totalCollected()));
      row.add(formatAmount(response.dueAmount()));

      csvData.add(row);
    }

    return csvData;
  }

  private List<List<String>> buildStudentTermWiseCsvData(List<FeeDto.FeeReportResponse> reportData) {
    return reportData.stream()
            .map(response -> Arrays.asList(
                    response.studentName(),
                    response.admissionNumber(),
                    response.className(),
                    response.fatherName(),
                    response.rollNumber(),
                    response.studentStatus(),
                    response.feeHead(),
                    response.term(),
                    formatAmount(response.amount()),
                    response.dueDate()
            ))
            .collect(Collectors.toList());
  }

  private Set<String> extractFeeTypesFromHeaders(List<String> headers) {
    Set<String> feeTypes = new LinkedHashSet<>();
    boolean inFeeSection = false;

    for (String header : headers) {
      if (header.equals("Student Status")) {
        inFeeSection = true;
        continue;
      }
      if (header.equals("Total")) {
        break;
      }
      if (inFeeSection) {
        feeTypes.add(header);
      }
    }

    return feeTypes;
  }

  private void generateCsvResponse(
          List<List<String>> csvData, List<String> headers, String fileName, HttpServletResponse response) {

    response.setContentType("text/csv; charset=UTF-8");
    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

    if (csvData.isEmpty()) {
      CsvUtils.generateCsv(headers.toArray(new String[0]), new ArrayList<>(), response);
      return;
    }

    CsvUtils.generateCsv(headers.toArray(new String[0]), csvData, response);
  }

  private String formatDate(Object dateObj) {
    if (dateObj == null) return "";
    if (dateObj instanceof LocalDateTime) {
      return ((LocalDateTime) dateObj).format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));
    }
    return dateObj.toString();
  }
}
