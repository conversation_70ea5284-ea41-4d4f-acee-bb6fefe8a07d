package com.wexl.erp.fees.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.erp.fees.repository.FeePaymentDetailsRepository;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.model.Student;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.CsvUtils;
import jakarta.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FeeDueReportService {

  private final FeeHeadRepository feeHeadRepository;
  private final FeePaymentDetailsRepository feePaymentDetailsRepository;
  private final StudentRepository studentRepository;
  private final SectionRepository sectionRepository;
  private final UserService userService;
  private final DateTimeUtil dateTimeUtil;
  private final GuardianService guardianService;

  private static final List<String> CSV_HEADERS =
      Arrays.asList(
          "STUDENT NAME",
          "FATHER NAME",
          "MOTHER NAME",
          "GUARDIAN NAME",
          "MOBILE NUMBER",
          "ADMISSION NUMBER",
          "ROLL NUMBER",
          "SECTION NAME",
          "DATE OF ADMISSION");

  private static final List<String> FEE_HEAD_MASTER_HEADERS = Arrays.asList(
          "Sno", "Admission Number", "Name of Student", "Father Name", "Father Mobile No",
          "Mother Name", "Roll No", "Mother Mobile No", "Class", "Gender", "Student Category",
          "Date of Admission", "Student Status");

  private static final List<String> STUDENT_TERM_WISE_HEADERS = Arrays.asList(
          "Student Name", "Student Registration Id", "Class", "Father Name",
          "Class Roll Number", "Student Status", "Fee Head", "Term", "Amount", "Due Date");

  public void generateFeeDueReportCsv(
      String orgSlug, FeeDto.FeeDueReportRequest request, HttpServletResponse response) {
    String reportType = request.reportType() != null ? request.reportType() : "total_due";

    if ("fee_head_master".equals(reportType) || "student_term_wise".equals(reportType)) {
      generateNewReportTypeCsv(orgSlug, request, response, reportType);
    } else {
      List<FeeHead> currentFeeHeads = new ArrayList<>();
      List<FeeDto.FeeDueReportResponse> reportData =
          generateFeeDueReport(orgSlug, request, currentFeeHeads);
      generateCsvResponse(reportData, response, reportType, request, currentFeeHeads);
    }
  }

  public List<FeeDto.FeeDueReportResponse> generateFeeDueReport(
      String orgSlug, FeeDto.FeeDueReportRequest request, List<FeeHead> currentFeeHeads) {
    List<Student> students = getStudentsBySectionUuids(request);
    List<FeeHead> feeHeads = getFeeHeadsByReportType(orgSlug, students, request);

    currentFeeHeads.addAll(feeHeads);
    Map<Student, List<FeeHead>> feeHeadsByStudent =
        feeHeads.stream().collect(Collectors.groupingBy(FeeHead::getStudent));

    String reportType = request.reportType() != null ? request.reportType() : "total_due";
    return students.stream()
        .map(
            student -> {
              List<FeeHead> studentFeeHeads = feeHeadsByStudent.get(student);
              if ("past_due".equals(reportType)) {
                if (studentFeeHeads == null
                    || studentFeeHeads.stream()
                            .map(FeeHead::getBalanceAmount)
                            .filter(Objects::nonNull)
                            .mapToDouble(Double::doubleValue)
                            .sum()
                        <= 0) {
                  return null;
                }
              }
              Double discount = calculateDiscount(student);
              return buildFeeDueReportResponse(discount, student, studentFeeHeads);
            })
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  private Double calculateDiscount(Student student) {
    var feeHeadList = feeHeadRepository.findAllByStudent(student);
    return (feeHeadList == null || feeHeadList.isEmpty())
        ? 0.0
        : feeHeadList.stream()
            .map(FeeHead::getDiscountAmount)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();
  }

  public List<Student> getStudentsBySectionUuids(FeeDto.FeeDueReportRequest request) {
    List<Section> sections =
        sectionRepository.findAllByUuidIn(
            request.sectionUuids().stream().map(UUID::fromString).toList());
    List<Student> students = new ArrayList<>();
    for (Section section : sections) {
      List<Student> sectionStudents = studentRepository.getStudentsBySection(section);
      students.addAll(sectionStudents);
    }
    return students;
  }

  private List<FeeHead> getFeeHeadsByReportType(
      String orgSlug, List<Student> students, FeeDto.FeeDueReportRequest request) {

    List<Long> studentIds = students.stream().map(Student::getId).collect(Collectors.toList());
    List<String> feeNames = request.feeGroupTypes();

    String reportType =
        request.reportType() != null ? request.reportType().toLowerCase() : "total_due";

    return switch (reportType) {
      case "past_due" ->
          feeHeadRepository.findPastDueFeeDetails(
              orgSlug,
              studentIds,
              feeNames,
              dateTimeUtil.convertEpochToIso8601(request.fromDate()),
              dateTimeUtil.convertEpochToIso8601(request.toDate()));
      case "total_due" ->
          feeHeadRepository.findTotalDueFeeDetails(
              orgSlug,
              studentIds,
              feeNames,
              dateTimeUtil.convertEpochToIso8601(request.fromDate()),
              dateTimeUtil.convertEpochToIso8601(request.toDate()));
      default -> Collections.emptyList();
    };
  }

  private List<FeeHead> getOptimizedFeeHeadsForNewReports(
      String orgSlug, List<Student> students, FeeDto.FeeDueReportRequest request) {

    List<Long> studentIds = students.stream().map(Student::getId).collect(Collectors.toList());

    return feeHeadRepository.findAllByOrgSlugAndStudentIds(orgSlug, studentIds);
  }

  private Map<Long, List<PaymentDetail>> getPaymentDetailsByStudents(List<Student> students, String orgSlug) {
    Map<Long, List<PaymentDetail>> paymentsByStudent = new HashMap<>();

    for (Student student : students) {
      List<FeeHead> studentFeeHeads = feeHeadRepository.findAllByStudent(student);
      List<PaymentDetail> studentPayments = new ArrayList<>();

      for (FeeHead feeHead : studentFeeHeads) {
        if (feeHead.getPaidAmount() != null && feeHead.getPaidAmount() > 0) {
          var paymentDetails = feePaymentDetailsRepository.findByFeeHead(feeHead);

          for (var paymentDetail : paymentDetails) {
            PaymentDetail payment = new PaymentDetail();
            payment.feeTypeName = feeHead.getFeeType().getName();
            payment.amount = paymentDetail.getAmountPaid();
            payment.date = formatDate(paymentDetail.getCreatedAt());
            payment.receiptNumber = paymentDetail.getFeePayment().getReferenceId() != null ?
                paymentDetail.getFeePayment().getReferenceId() : "";

            studentPayments.add(payment);
          }
        }
      }

      paymentsByStudent.put(student.getId(), studentPayments);
    }

    return paymentsByStudent;
  }

  private FeeDto.FeeDueReportResponse buildFeeDueReportResponse(
      Double discount, Student student, List<FeeHead> feeHeads) {
    if (feeHeads == null || feeHeads.isEmpty()) {
      return null;
    }

    List<FeeDto.FeeDetailResponse> feeDetails =
        feeHeads.stream().map(this::buildFeeDetailResponse).collect(Collectors.toList());

    Double totalDueAmount =
        feeHeads.stream()
            .mapToDouble(
                feeHead -> feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
            .sum();

    return FeeDto.FeeDueReportResponse.builder()
        .studentName(userService.getNameByUserInfo(student.getUserInfo()))
        .fatherName(
            student.getGuardians().stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.FATHER))
                .findAny()
                .map(guardianService::getGuardianName)
                .orElse(null))
        .motherName(
            student.getGuardians().stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.MOTHER))
                .findAny()
                .map(guardianService::getGuardianName)
                .orElse(null))
        .guardianName(
            student.getGuardians().stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.GUARDIAN))
                .findAny()
                .map(guardianService::getGuardianName)
                .orElse(null))
        .mobileNumber(student.getUserInfo().getMobileNumber())
        .admissionNumber(student.getUserInfo().getUserName())
        .rollNumber(student.getClassRollNumber())
        .sectionName(student.getSection() != null ? student.getSection().getName() : "")
        .dateOfAdmission(
            student.getCreatedAt() != null
                ? student
                    .getCreatedAt()
                    .toLocalDateTime()
                    .format(DateTimeFormatter.ofPattern("dd-MM-yyyy"))
                : "")
        .feeDetails(feeDetails)
        .discountAmount(discount)
        .totalDueAmount(totalDueAmount)
        .build();
  }

  private FeeDto.FeeDetailResponse buildFeeDetailResponse(FeeHead feeHead) {
    return FeeDto.FeeDetailResponse.builder()
        .feeTypeName(feeHead.getFeeType().getName())
        .month(
            feeHead.getDueDate() != null
                ? feeHead.getDueDate().format(DateTimeFormatter.ofPattern("MMM")).toUpperCase()
                : "")
        .amount(feeHead.getAmount())
        .paidAmount(feeHead.getPaidAmount() != null ? feeHead.getPaidAmount() : 0.0)
        .balanceAmount(feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
        .dueDate(convertIso8601ToEpoch(feeHead.getDueDate()))
        .status(feeHead.getStatus())
        .build();
  }

  private void generateCsvResponse(
      List<FeeDto.FeeDueReportResponse> reportData,
      HttpServletResponse response,
      String reportType,
      FeeDto.FeeDueReportRequest request,
      List<FeeHead> currentFeeHeads) {

    String fileName = getFileName(reportType);
    response.setContentType("text/csv; charset=UTF-8");
    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

    List<List<String>> csvData = buildCsvBody(reportData, request, currentFeeHeads);
    if (csvData.isEmpty()) {
      CsvUtils.generateCsv(CSV_HEADERS.toArray(new String[0]), new ArrayList<>(), response);
      return;
    }

    List<String> csvHeaders = csvData.removeFirst();
    CsvUtils.generateCsv(csvHeaders.toArray(new String[0]), csvData, response);
  }

  private String getFileName(String reportType) {
    String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm_dd_MM_yyyy"));
    return switch (reportType) {
      case "past_due" -> "past_due_report_" + timestamp + ".csv";
      case "total_due" -> "total_due_report_" + timestamp + ".csv";
      case "fee_head_master" -> "fee_head_master_report_" + timestamp + ".csv";
      case "student_term_wise" -> "student_term_wise_report_" + timestamp + ".csv";
      default -> "fee_due_report_" + timestamp + ".csv";
    };
  }

  private List<List<String>> buildCsvBody(
      List<FeeDto.FeeDueReportResponse> reportData,
      FeeDto.FeeDueReportRequest request,
      List<FeeHead> currentFeeHeads) {
    List<List<String>> csvBody = new ArrayList<>();

    if (reportData.isEmpty()) {
      return csvBody;
    }

    List<String> headers = generateDynamicHeaders(request, currentFeeHeads);
    csvBody.add(headers);

    for (FeeDto.FeeDueReportResponse report : reportData) {
      List<String> row = buildDynamicDataRow(report, request, currentFeeHeads);
      csvBody.add(row);
    }

    return csvBody;
  }

  private List<String> generateDynamicHeaders(
      FeeDto.FeeDueReportRequest request, List<FeeHead> currentFeeHeads) {

    List<String> headers = new ArrayList<>(CSV_HEADERS);
    Map<String, Set<String>> feeGroupStructure = buildFeeGroupStructure(request, currentFeeHeads);

    for (Map.Entry<String, Set<String>> entry : feeGroupStructure.entrySet()) {
      String feeGroupDescription = entry.getKey();
      Set<String> feeTypes = entry.getValue();

      if (feeTypes.isEmpty()) {
        headers.add(feeGroupDescription);
      } else {
        for (String feeType : feeTypes) {
          headers.add(feeGroupDescription + " " + feeType);
        }
      }
    }

    headers.add("DISCOUNT AMOUNT");
    headers.add("TOTAL DUE AMOUNT");
    //    headers.addAll(List.of("Delete", "EXTENDED DAY", "Fee Remark"));

    return headers;
  }

  private Map<String, Set<String>> buildFeeGroupStructure(
      FeeDto.FeeDueReportRequest request, List<FeeHead> currentFeeHeads) {
    Map<String, Set<String>> feeGroupStructure = new LinkedHashMap<>();

    if (currentFeeHeads != null) {
      for (FeeHead feeHead : currentFeeHeads) {
        if (feeHead.getFeeMaster() != null
            && feeHead.getFeeMaster().getFeeGroup() != null
            && feeHead.getFeeType() != null) {
          String feeGroupDescription = feeHead.getFeeMaster().getFeeGroup().getName();
          String feeTypeName = feeHead.getFeeType().getName();

          feeGroupStructure
              .computeIfAbsent(feeGroupDescription, k -> new LinkedHashSet<>())
              .add(feeTypeName);
        }
      }
    }

    List<String> requestedFeeGroupTypes = request.feeGroupTypes();
    if (requestedFeeGroupTypes != null && !requestedFeeGroupTypes.isEmpty()) {
      Map<String, Set<String>> filteredStructure = new LinkedHashMap<>();
      for (String requestedType : requestedFeeGroupTypes) {
        if (feeGroupStructure.containsKey(requestedType)) {
          filteredStructure.put(requestedType, feeGroupStructure.get(requestedType));
        }
      }
      return filteredStructure;
    }

    return feeGroupStructure;
  }

  private String getFeeGroupDescriptionForStudent(
      Long studentId, String feeTypeName, List<FeeHead> currentFeeHeads) {
    if (currentFeeHeads == null || studentId == null || feeTypeName == null) {
      return "Unknown Fee Group";
    }

    for (FeeHead feeHead : currentFeeHeads) {
      if (feeHead.getStudent() != null
          && feeHead.getStudent().getId() == (studentId)
          && feeHead.getFeeType() != null
          && feeTypeName.equals(feeHead.getFeeType().getName())
          && feeHead.getFeeMaster() != null
          && feeHead.getFeeMaster().getFeeGroup() != null) {
        return feeHead.getFeeMaster().getFeeGroup().getName();
      }
    }
    return "Unknown Fee Group";
  }

  private List<String> buildDynamicDataRow(
      FeeDto.FeeDueReportResponse report,
      FeeDto.FeeDueReportRequest request,
      List<FeeHead> currentFeeHeads) {
    List<String> row = new ArrayList<>();

    row.add(report.studentName() != null ? report.studentName() : "");
    row.add(report.fatherName() != null ? report.fatherName() : "");
    row.add(report.motherName() != null ? report.motherName() : "");
    row.add(report.guardianName() != null ? report.guardianName() : "");
    row.add(report.mobileNumber() != null ? report.mobileNumber() : "");
    row.add(report.admissionNumber() != null ? report.admissionNumber() : "");
    row.add(report.rollNumber() != null ? report.rollNumber() : "");
    row.add(report.sectionName() != null ? report.sectionName() : "");
    row.add(report.dateOfAdmission() != null ? report.dateOfAdmission() : "");

    Map<String, Map<String, Double>> feeGroupAmounts =
        buildFeeGroupAmountsMap(report, currentFeeHeads);
    Map<String, Set<String>> feeGroupStructure = buildFeeGroupStructure(request, currentFeeHeads);

    for (Map.Entry<String, Set<String>> entry : feeGroupStructure.entrySet()) {
      String feeGroupDescription = entry.getKey();
      Set<String> feeTypes = entry.getValue();

      if (feeTypes.isEmpty()) {
        Double amount =
            feeGroupAmounts.getOrDefault(feeGroupDescription, new HashMap<>()).values().stream()
                .mapToDouble(Double::doubleValue)
                .sum();
        row.add(String.format("%.0f", amount));
      } else {
        for (String feeType : feeTypes) {
          Double amount =
              feeGroupAmounts
                  .getOrDefault(feeGroupDescription, new HashMap<>())
                  .getOrDefault(feeType, 0.0);
          row.add(String.format("%.0f", amount));
        }
      }
    }

    row.add(String.format("%.0f", report.discountAmount()));
    row.add(String.format("%.0f", report.totalDueAmount()));
    //    row.add("0");
    //    row.add("0");
    //    row.add("");

    return row;
  }

  private Map<String, Map<String, Double>> buildFeeGroupAmountsMap(
      FeeDto.FeeDueReportResponse report, List<FeeHead> currentFeeHeads) {
    Map<String, Map<String, Double>> feeGroupAmounts = new HashMap<>();

    Long studentId = getStudentIdFromReport(report, currentFeeHeads);

    for (FeeDto.FeeDetailResponse detail : report.feeDetails()) {
      String feeGroupDescription =
          getFeeGroupDescriptionForStudent(studentId, detail.feeTypeName(), currentFeeHeads);
      String feeTypeName = detail.feeTypeName();
      Double amount = detail.balanceAmount() != null ? detail.balanceAmount() : 0.0;

      feeGroupAmounts
          .computeIfAbsent(feeGroupDescription, k -> new HashMap<>())
          .merge(feeTypeName, amount, Double::sum);
    }

    return feeGroupAmounts;
  }

  private Long getStudentIdFromReport(
      FeeDto.FeeDueReportResponse report, List<FeeHead> currentFeeHeads) {
    String admissionNumber = report.admissionNumber();
    if (admissionNumber == null) {
      return null;
    }

    for (FeeHead feeHead : currentFeeHeads) {
      if (feeHead.getStudent() != null
          && feeHead.getStudent().getUserInfo() != null
          && admissionNumber.equals(feeHead.getStudent().getUserInfo().getUserName())) {
        return feeHead.getStudent().getId();
      }
    }
    return null;
  }

  private String formatDate(Object dateObj) {
    if (dateObj == null) return "";
    if (dateObj instanceof LocalDateTime) {
      return ((LocalDateTime) dateObj).format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));
    }
    return dateObj.toString();
  }

  // New methods for fee_head_master and student_term_wise reports
  private void generateNewReportTypeCsv(
      String orgSlug, FeeDto.FeeDueReportRequest request, HttpServletResponse response, String reportType) {

    if ("fee_head_master".equals(reportType)) {
      generateFeeHeadMasterReportCsv(orgSlug, request, response);
    } else if ("student_term_wise".equals(reportType)) {
      generateStudentTermWiseReportCsv(orgSlug, request, response);
    }
  }

  private void generateFeeHeadMasterReportCsv(
      String orgSlug, FeeDto.FeeDueReportRequest request, HttpServletResponse response) {

    try {
      log.info("Generating Fee Head Master Report for orgSlug: {}", orgSlug);

      // Validate input parameters
      if (request.sectionUuids() == null || request.sectionUuids().isEmpty()) {
        log.warn("No section UUIDs provided for Fee Head Master Report");
        generateEmptyReport(response, "fee_head_master");
        return;
      }

      // Get comprehensive data with optimized queries
      List<Student> students = getStudentsBySectionUuids(request);
      if (students.isEmpty()) {
        log.warn("No students found for provided section UUIDs");
        generateEmptyReport(response, "fee_head_master");
        return;
      }

      List<FeeHead> feeHeads = getOptimizedFeeHeadsForNewReports(orgSlug, students, request);
      Map<Long, List<PaymentDetail>> paymentsByStudent = getPaymentDetailsByStudents(students, orgSlug);

      // Process data to group by student and fee types
      Map<Long, StudentFeeData> studentDataMap = processFeeHeadMasterData(students, feeHeads, paymentsByStudent);

      // Generate two-row headers based on fee types found in data
      HeaderStructure headerStructure = generateFeeHeadMasterHeaders(feeHeads);

      // Build CSV data with two-row headers
      List<List<String>> csvData = buildFeeHeadMasterCsvData(studentDataMap, headerStructure);

      // Generate CSV response with two-row headers
      generateTwoRowHeaderCsvResponse(csvData, headerStructure, getFileName("fee_head_master"), response);

      log.info("Successfully generated Fee Head Master Report with {} students", students.size());

    } catch (Exception e) {
      log.error("Error generating Fee Head Master Report for orgSlug: {}", orgSlug, e);
      throw new RuntimeException("Failed to generate Fee Head Master Report", e);
    }
  }

  private void generateStudentTermWiseReportCsv(
      String orgSlug, FeeDto.FeeDueReportRequest request, HttpServletResponse response) {

    try {
      log.info("Generating Student Term-wise Report for orgSlug: {}", orgSlug);

      // Validate input parameters
      if (request.sectionUuids() == null || request.sectionUuids().isEmpty()) {
        log.warn("No section UUIDs provided for Student Term-wise Report");
        generateEmptyReport(response, "student_term_wise");
        return;
      }

      // Get comprehensive data with optimized queries
      List<Student> students = getStudentsBySectionUuids(request);
      if (students.isEmpty()) {
        log.warn("No students found for provided section UUIDs");
        generateEmptyReport(response, "student_term_wise");
        return;
      }

      List<FeeHead> feeHeads = getOptimizedFeeHeadsForNewReports(orgSlug, students, request);

      // Process data for term-wise report
      List<StudentTermData> termData = processStudentTermWiseData(students, feeHeads, request);

      // Build CSV data
      List<List<String>> csvData = buildStudentTermWiseCsvData(termData);

      // Generate CSV response with single header
      generateSingleHeaderCsvResponse(csvData, STUDENT_TERM_WISE_HEADERS, getFileName("student_term_wise"), response);

      log.info("Successfully generated Student Term-wise Report with {} records", termData.size());

    } catch (Exception e) {
      log.error("Error generating Student Term-wise Report for orgSlug: {}", orgSlug, e);
      throw new RuntimeException("Failed to generate Student Term-wise Report", e);
    }
  }

  private Map<Long, StudentFeeData> processFeeHeadMasterData(
      List<Student> students, List<FeeHead> feeHeads, Map<Long, List<PaymentDetail>> paymentsByStudent) {

    Map<Long, StudentFeeData> studentDataMap = new HashMap<>();
    Map<Long, List<FeeHead>> feeHeadsByStudent =
        feeHeads.stream().collect(Collectors.groupingBy(fh -> fh.getStudent().getId()));

    int sno = 1;
    for (Student student : students) {
      StudentFeeData studentData = extractStudentBasicInfo(student, sno++);

      studentData.feeApplicable = new HashMap<>();
      studentData.feeCollected = new HashMap<>();

      // Process fee heads for this student
      List<FeeHead> studentFeeHeads = feeHeadsByStudent.getOrDefault(student.getId(), new ArrayList<>());
      for (FeeHead feeHead : studentFeeHeads) {
        String feeTypeName = feeHead.getFeeType().getName();
        Double feeAmount = feeHead.getAmount() != null ? feeHead.getAmount() : 0.0;

        // Accumulate fee amounts by type (in case there are multiple fee heads of same type)
        studentData.feeApplicable.merge(feeTypeName, feeAmount, Double::sum);
      }

      // Process payment details
      List<PaymentDetail> studentPayments = paymentsByStudent.getOrDefault(student.getId(), new ArrayList<>());
      for (PaymentDetail payment : studentPayments) {
        if (!studentData.feeCollected.containsKey(payment.feeTypeName)) {
          studentData.feeCollected.put(payment.feeTypeName, new ArrayList<>());
        }
        studentData.feeCollected.get(payment.feeTypeName).add(payment);
      }

      // Calculate totals
      studentData.grandTotal = studentData.feeApplicable.values().stream()
          .mapToDouble(Double::doubleValue).sum();
      studentData.totalCollected = studentPayments.stream()
          .mapToDouble(payment -> payment.amount)
          .sum();
      studentData.discounts = calculateDiscount(student);
      studentData.dueAmount = studentData.grandTotal - studentData.totalCollected - studentData.discounts;

      studentDataMap.put(student.getId(), studentData);
    }

    return studentDataMap;
  }

  // Reusable method to extract basic student information
  private StudentFeeData extractStudentBasicInfo(Student student, int sno) {
    StudentFeeData studentData = new StudentFeeData();
    studentData.sno = sno;
    studentData.admissionNumber = student.getUserInfo().getUserName();
    studentData.studentName = buildFullName(student.getUserInfo().getFirstName(), student.getUserInfo().getLastName());

    // Get guardian information efficiently
    var guardianInfo = extractGuardianInfo(student);
    studentData.fatherName = guardianInfo.fatherName;
    studentData.fatherMobile = guardianInfo.fatherMobile;
    studentData.motherName = guardianInfo.motherName;
    studentData.motherMobile = guardianInfo.motherMobile;

    studentData.rollNumber = student.getRollNumber();
    studentData.classRollNumber = student.getClassRollNumber();
    studentData.className = student.getSection() != null ? student.getSection().getName() : "";
    studentData.gender = student.getUserInfo().getGender() != null ? student.getUserInfo().getGender().toString() : "";
    studentData.studentCategory = "DAY SCHOLAR";
    studentData.dateOfAdmission = formatDate(student.getCreatedAt());
    studentData.studentStatus = student.getActive() == '1' ? "ACTIVE" : "INACTIVE";

    return studentData;
  }

  private GuardianInfo extractGuardianInfo(Student student) {
    var guardians = guardianService.fetchGuardiansDetails(student.getUserInfo().getAuthUserId());
    var father = guardians.

    GuardianInfo info = new GuardianInfo();
    info.fatherName = father.map(g -> g.getFirstName()).orElse("");
    info.fatherMobile = father.map(g -> g.getMobileNumber()).orElse("");
    info.motherName = mother.map(g -> g.getFirstName()).orElse("");
    info.motherMobile = mother.map(g -> g.getMobileNumber()).orElse("");

    return info;
  }

  private String buildFullName(String firstName, String lastName) {
    return firstName + (lastName != null ? " " + lastName : "");
  }

  private HeaderStructure generateFeeHeadMasterHeaders(List<FeeHead> feeHeads) {
    Set<String> feeTypes = feeHeads.stream()
        .map(fh -> fh.getFeeType().getName())
        .collect(Collectors.toCollection(LinkedHashSet::new));

    Map<String, String> feeGroupDescriptions = feeHeads.stream()
        .collect(Collectors.toMap(
            fh -> fh.getFeeType().getName(),
            fh -> {
              String description = fh.getFeeMaster().getFeeGroup().getDescription();
              return description != null && !description.trim().isEmpty() ?
                     description : fh.getFeeMaster().getFeeGroup().getName();
            },
            (existing, replacement) -> existing,
            LinkedHashMap::new
        ));

    HeaderStructure structure = new HeaderStructure();

    List<String> mainHeaders = new ArrayList<>(FEE_HEAD_MASTER_HEADERS);

    mainHeaders.add("FEE APPLICABLE");
    for (int i = 1; i < feeTypes.size(); i++) {
      mainHeaders.add("");
    }

    mainHeaders.add("Total");
    mainHeaders.add("Discounts");
    mainHeaders.add("Grand Total");

    mainHeaders.add("FEE COLLECTED");
    for (String feeType : feeTypes) {
      mainHeaders.add(feeType);
      mainHeaders.add("");
      mainHeaders.add("");
    }
    mainHeaders.add("Total");
    mainHeaders.add("Due");

    List<String> subHeaders = new ArrayList<>();

    // Basic student info columns - use actual header names
    subHeaders.addAll(FEE_HEAD_MASTER_HEADERS);

    // Fee applicable sub headers - use actual fee type names from database
    subHeaders.addAll(feeTypes);
    subHeaders.add("Total");
    subHeaders.add("Discounts");
    subHeaders.add("Grand Total");

    // Fee collected sub headers - use meaningful column names
    for (String feeType : feeTypes) {
      subHeaders.add("Date");
      subHeaders.add("Receipt No");
      subHeaders.add("Amount");
    }
    subHeaders.add("Total");
    subHeaders.add("Due");

    structure.mainHeaders = mainHeaders;
    structure.subHeaders = subHeaders;
    structure.feeTypes = new ArrayList<>(feeTypes);
    structure.feeGroupDescriptions = feeGroupDescriptions;

    return structure;
  }

  private static class StudentFeeData {
    int sno;
    String admissionNumber;
    String studentName;
    String fatherName;
    String fatherMobile;
    String motherName;
    String motherMobile;
    String rollNumber;
    String classRollNumber;
    String className;
    String gender;
    String studentCategory;
    String dateOfAdmission;
    String studentStatus;
    Map<String, Double> feeApplicable;
    Map<String, List<PaymentDetail>> feeCollected;
    Double grandTotal;
    Double totalCollected;
    Double dueAmount;
    Double discounts;
  }

  private static class PaymentDetail {
    String feeTypeName;
    String date;
    String receiptNumber;
    Double amount;
  }

  private static class GuardianInfo {
    String fatherName;
    String fatherMobile;
    String motherName;
    String motherMobile;
  }

  private static class HeaderStructure {
    List<String> mainHeaders;
    List<String> subHeaders;
    List<String> feeTypes;
    Map<String, String> feeGroupDescriptions;
  }

  private static class StudentTermData {
    String studentName;
    String registrationId;
    String className;
    String fatherName;
    String classRollNumber;
    String studentStatus;
    String feeHead;
    String term;
    Double amount;
    String dueDate;
  }

  private List<StudentTermData> processStudentTermWiseData(
      List<Student> students, List<FeeHead> feeHeads, FeeDto.FeeDueReportRequest request) {

    List<StudentTermData> termDataList = new ArrayList<>();
    Map<Long, List<FeeHead>> feeHeadsByStudent =
        feeHeads.stream().collect(Collectors.groupingBy(fh -> fh.getStudent().getId()));

    for (Student student : students) {
      List<FeeHead> studentFeeHeads = feeHeadsByStudent.getOrDefault(student.getId(), new ArrayList<>());

      var guardianInfo = extractGuardianInfo(student);

      for (FeeHead feeHead : studentFeeHeads) {
        if (shouldIncludeFeeHeadInTermReport(feeHead, request)) {
          StudentTermData termData = new StudentTermData();
          termData.studentName = buildFullName(student.getUserInfo().getFirstName(), student.getUserInfo().getLastName());
          termData.registrationId = student.getUserInfo().getUserName();
          termData.className = student.getSection() != null ? student.getSection().getName() : "";
          termData.fatherName = guardianInfo.fatherName;
          termData.classRollNumber = student.getClassRollNumber();
          termData.studentStatus = student.getActive() == '1' ? "active" : "inactive";
          termData.feeHead = feeHead.getFeeType().getName();
          termData.term = determineTerm(feeHead, request);
          termData.amount = feeHead.getAmount() != null ? feeHead.getAmount() : 0.0;
          termData.dueDate = formatDate(feeHead.getDueDate());

          termDataList.add(termData);
        }
      }
    }

    return termDataList;
  }

  private boolean shouldIncludeFeeHeadInTermReport(FeeHead feeHead, FeeDto.FeeDueReportRequest request) {
    if (request.reportType() != null && request.reportType().contains("term")) {
      return true;
    }

    return true;
  }

  private String determineTerm(FeeHead feeHead, FeeDto.FeeDueReportRequest request) {
    return "Term 1";
  }

  private List<List<String>> buildFeeHeadMasterCsvData(
      Map<Long, StudentFeeData> studentDataMap, HeaderStructure headerStructure) {

    List<List<String>> csvData = new ArrayList<>();

    for (StudentFeeData studentData : studentDataMap.values()) {
      List<String> row = new ArrayList<>();

      row.add(String.valueOf(studentData.sno));
      row.add(safeString(studentData.admissionNumber));
      row.add(safeString(studentData.studentName));
      row.add(safeString(studentData.fatherName));
      row.add(safeString(studentData.fatherMobile));
      row.add(safeString(studentData.motherName));
      row.add(safeString(studentData.rollNumber));
      row.add(safeString(studentData.motherMobile));
      row.add(safeString(studentData.className));
      row.add(safeString(studentData.gender));
      row.add(safeString(studentData.studentCategory));
      row.add(safeString(studentData.dateOfAdmission));
      row.add(safeString(studentData.studentStatus));

      for (String feeType : headerStructure.feeTypes) {
        Double amount = studentData.feeApplicable.getOrDefault(feeType, 0.0);
        row.add(formatAmount(amount));
      }

      row.add(formatAmount(studentData.grandTotal));
      row.add(formatAmount(studentData.discounts));
      row.add(formatAmount(studentData.grandTotal - studentData.discounts));

      for (String feeType : headerStructure.feeTypes) {
        List<PaymentDetail> payments = studentData.feeCollected.getOrDefault(feeType, new ArrayList<>());
        if (!payments.isEmpty()) {
          PaymentDetail latestPayment = payments.get(payments.size() - 1);
          row.add(safeString(latestPayment.date));
          row.add(safeString(latestPayment.receiptNumber));
          row.add(formatAmount(latestPayment.amount));
        } else {
          row.add("");
          row.add("");
          row.add("0");
        }
      }

      row.add(formatAmount(studentData.totalCollected));
      row.add(formatAmount(studentData.dueAmount));

      csvData.add(row);
    }

    return csvData;
  }

  private String safeString(String value) {
    return value != null ? value : "";
  }

  private List<List<String>> buildStudentTermWiseCsvData(List<StudentTermData> termDataList) {
    return termDataList.stream()
        .map(termData -> Arrays.asList(
            safeString(termData.studentName),
            safeString(termData.registrationId),
            safeString(termData.className),
            safeString(termData.fatherName),
            safeString(termData.classRollNumber),
            safeString(termData.studentStatus),
            safeString(termData.feeHead),
            safeString(termData.term),
            formatAmount(termData.amount),
            safeString(termData.dueDate)
        ))
        .collect(Collectors.toList());
  }

  private void generateTwoRowHeaderCsvResponse(
      List<List<String>> csvData, HeaderStructure headerStructure, String fileName, HttpServletResponse response) {

    response.setContentType("text/csv; charset=UTF-8");
    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

    try {
      var writer = response.getWriter();

      String mainHeaderLine = headerStructure.mainHeaders.stream()
          .map(this::escapeCsvValue)
          .collect(Collectors.joining(","));
      writer.println(mainHeaderLine);

      String subHeaderLine = headerStructure.subHeaders.stream()
          .map(this::escapeCsvValue)
          .collect(Collectors.joining(","));
      writer.println(subHeaderLine);

      for (List<String> row : csvData) {
        String dataLine = row.stream()
            .map(this::escapeCsvValue)
            .collect(Collectors.joining(","));
        writer.println(dataLine);
      }

      writer.flush();
    } catch (Exception e) {
      log.error("Error generating two-row header CSV response for file: {}", fileName, e);
      throw new RuntimeException("Error generating CSV response", e);
    }
  }

  private void generateSingleHeaderCsvResponse(
      List<List<String>> csvData, List<String> headers, String fileName, HttpServletResponse response) {

    response.setContentType("text/csv; charset=UTF-8");
    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

    try {
      if (csvData.isEmpty()) {
        CsvUtils.generateCsv(headers.toArray(new String[0]), new ArrayList<>(), response);
        return;
      }

      CsvUtils.generateCsv(headers.toArray(new String[0]), csvData, response);
    } catch (Exception e) {
      log.error("Error generating single header CSV response for file: {}", fileName, e);
      throw new RuntimeException("Error generating CSV response", e);
    }
  }

  private String escapeCsvValue(String value) {
    if (value == null) return "";
    if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
      return "\"" + value.replace("\"", "\"\"") + "\"";
    }
    return value;
  }
}
