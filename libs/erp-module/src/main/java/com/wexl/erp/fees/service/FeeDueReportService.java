package com.wexl.erp.fees.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.model.Student;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.CsvUtils;
import jakarta.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FeeDueReportService {

  private final FeeHeadRepository feeHeadRepository;
  private final StudentRepository studentRepository;
  private final SectionRepository sectionRepository;
  private final UserService userService;
  private final DateTimeUtil dateTimeUtil;
  private final GuardianService guardianService;

  private static final List<String> CSV_HEADERS =
      Arrays.asList(
          "STUDENT NAME",
          "FATHER NAME",
          "MOTHER NAME",
          "GUARDIAN NAME",
          "MOBILE NUMBER",
          "ADMISSION NUMBER",
          "ROLL NUMBER",
          "SECTION NAME",
          "DATE OF ADMISSION");

  private static final List<String> FEE_HEAD_MASTER_HEADERS = Arrays.asList(
          "Sno", "Admission Number", "Name of Student", "Father Name", "Father Mobile No",
          "Mother Name", "Roll No", "Mother Mobile No", "Class", "Gender", "Student Category",
          "Date of Admission", "Student Status");

  private static final List<String> STUDENT_TERM_WISE_HEADERS = Arrays.asList(
          "Student Name", "Student Registration Id", "Class", "Father Name",
          "Class Roll Number", "Student Status", "Fee Head", "Term", "Amount", "Due Date");

  public void generateFeeDueReportCsv(
      String orgSlug, FeeDto.FeeDueReportRequest request, HttpServletResponse response) {
    String reportType = request.reportType() != null ? request.reportType() : "total_due";

    if ("fee_head_master".equals(reportType) || "student_term_wise".equals(reportType)) {
      generateNewReportTypeCsv(orgSlug, request, response, reportType);
    } else {
      // Existing logic for total_due and past_due reports
      List<FeeHead> currentFeeHeads = new ArrayList<>();
      List<FeeDto.FeeDueReportResponse> reportData =
          generateFeeDueReport(orgSlug, request, currentFeeHeads);
      generateCsvResponse(reportData, response, reportType, request, currentFeeHeads);
    }
  }

  public List<FeeDto.FeeDueReportResponse> generateFeeDueReport(
      String orgSlug, FeeDto.FeeDueReportRequest request, List<FeeHead> currentFeeHeads) {
    List<Student> students = getStudentsBySectionUuids(request);
    List<FeeHead> feeHeads = getFeeHeadsByReportType(orgSlug, students, request);

    currentFeeHeads.addAll(feeHeads);
    Map<Student, List<FeeHead>> feeHeadsByStudent =
        feeHeads.stream().collect(Collectors.groupingBy(FeeHead::getStudent));

    String reportType = request.reportType() != null ? request.reportType() : "total_due";
    return students.stream()
        .map(
            student -> {
              List<FeeHead> studentFeeHeads = feeHeadsByStudent.get(student);
              if ("past_due".equals(reportType)) {
                if (studentFeeHeads == null
                    || studentFeeHeads.stream()
                            .map(FeeHead::getBalanceAmount)
                            .filter(Objects::nonNull)
                            .mapToDouble(Double::doubleValue)
                            .sum()
                        <= 0) {
                  return null;
                }
              }
              Double discount = calculateDiscount(student);
              return buildFeeDueReportResponse(discount, student, studentFeeHeads);
            })
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  private Double calculateDiscount(Student student) {
    var feeHeadList = feeHeadRepository.findAllByStudent(student);
    return (feeHeadList == null || feeHeadList.isEmpty())
        ? 0.0
        : feeHeadList.stream()
            .map(FeeHead::getDiscountAmount)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();
  }

  public List<Student> getStudentsBySectionUuids(FeeDto.FeeDueReportRequest request) {
    List<Section> sections =
        sectionRepository.findAllByUuidIn(
            request.sectionUuids().stream().map(UUID::fromString).toList());
    List<Student> students = new ArrayList<>();
    for (Section section : sections) {
      List<Student> sectionStudents = studentRepository.getStudentsBySection(section);
      students.addAll(sectionStudents);
    }
    return students;
  }

  private List<FeeHead> getFeeHeadsByReportType(
      String orgSlug, List<Student> students, FeeDto.FeeDueReportRequest request) {

    List<Long> studentIds = students.stream().map(Student::getId).collect(Collectors.toList());
    List<String> feeNames = request.feeGroupTypes();

    String reportType =
        request.reportType() != null ? request.reportType().toLowerCase() : "total_due";

    return switch (reportType) {
      case "past_due" ->
          feeHeadRepository.findPastDueFeeDetails(
              orgSlug,
              studentIds,
              feeNames,
              dateTimeUtil.convertEpochToIso8601(request.fromDate()),
              dateTimeUtil.convertEpochToIso8601(request.toDate()));
      case "total_due" ->
          feeHeadRepository.findTotalDueFeeDetails(
              orgSlug,
              studentIds,
              feeNames,
              dateTimeUtil.convertEpochToIso8601(request.fromDate()),
              dateTimeUtil.convertEpochToIso8601(request.toDate()));
      default -> Collections.emptyList();
    };
  }

  private FeeDto.FeeDueReportResponse buildFeeDueReportResponse(
      Double discount, Student student, List<FeeHead> feeHeads) {
    if (feeHeads == null || feeHeads.isEmpty()) {
      return null;
    }

    List<FeeDto.FeeDetailResponse> feeDetails =
        feeHeads.stream().map(this::buildFeeDetailResponse).collect(Collectors.toList());

    Double totalDueAmount =
        feeHeads.stream()
            .mapToDouble(
                feeHead -> feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
            .sum();

    return FeeDto.FeeDueReportResponse.builder()
        .studentName(userService.getNameByUserInfo(student.getUserInfo()))
        .fatherName(
            student.getGuardians().stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.FATHER))
                .findAny()
                .map(guardianService::getGuardianName)
                .orElse(null))
        .motherName(
            student.getGuardians().stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.MOTHER))
                .findAny()
                .map(guardianService::getGuardianName)
                .orElse(null))
        .guardianName(
            student.getGuardians().stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.GUARDIAN))
                .findAny()
                .map(guardianService::getGuardianName)
                .orElse(null))
        .mobileNumber(student.getUserInfo().getMobileNumber())
        .admissionNumber(student.getUserInfo().getUserName())
        .rollNumber(student.getClassRollNumber())
        .sectionName(student.getSection() != null ? student.getSection().getName() : "")
        .dateOfAdmission(
            student.getCreatedAt() != null
                ? student
                    .getCreatedAt()
                    .toLocalDateTime()
                    .format(DateTimeFormatter.ofPattern("dd-MM-yyyy"))
                : "")
        .feeDetails(feeDetails)
        .discountAmount(discount)
        .totalDueAmount(totalDueAmount)
        .build();
  }

  private FeeDto.FeeDetailResponse buildFeeDetailResponse(FeeHead feeHead) {
    return FeeDto.FeeDetailResponse.builder()
        .feeTypeName(feeHead.getFeeType().getName())
        .month(
            feeHead.getDueDate() != null
                ? feeHead.getDueDate().format(DateTimeFormatter.ofPattern("MMM")).toUpperCase()
                : "")
        .amount(feeHead.getAmount())
        .paidAmount(feeHead.getPaidAmount() != null ? feeHead.getPaidAmount() : 0.0)
        .balanceAmount(feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
        .dueDate(convertIso8601ToEpoch(feeHead.getDueDate()))
        .status(feeHead.getStatus())
        .build();
  }

  private void generateCsvResponse(
      List<FeeDto.FeeDueReportResponse> reportData,
      HttpServletResponse response,
      String reportType,
      FeeDto.FeeDueReportRequest request,
      List<FeeHead> currentFeeHeads) {

    String fileName = getFileName(reportType);
    response.setContentType("text/csv; charset=UTF-8");
    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

    List<List<String>> csvData = buildCsvBody(reportData, request, currentFeeHeads);
    if (csvData.isEmpty()) {
      CsvUtils.generateCsv(CSV_HEADERS.toArray(new String[0]), new ArrayList<>(), response);
      return;
    }

    List<String> csvHeaders = csvData.removeFirst();
    CsvUtils.generateCsv(csvHeaders.toArray(new String[0]), csvData, response);
  }

  private String getFileName(String reportType) {
    String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm_dd_MM_yyyy"));
    return switch (reportType) {
      case "past_due" -> "past_due_report_" + timestamp + ".csv";
      case "total_due" -> "total_due_report_" + timestamp + ".csv";
      case "fee_head_master" -> "fee_head_master_report_" + timestamp + ".csv";
      case "student_term_wise" -> "student_term_wise_report_" + timestamp + ".csv";
      default -> "fee_due_report_" + timestamp + ".csv";
    };
  }

  private List<List<String>> buildCsvBody(
      List<FeeDto.FeeDueReportResponse> reportData,
      FeeDto.FeeDueReportRequest request,
      List<FeeHead> currentFeeHeads) {
    List<List<String>> csvBody = new ArrayList<>();

    if (reportData.isEmpty()) {
      return csvBody;
    }

    List<String> headers = generateDynamicHeaders(request, currentFeeHeads);
    csvBody.add(headers);

    for (FeeDto.FeeDueReportResponse report : reportData) {
      List<String> row = buildDynamicDataRow(report, request, currentFeeHeads);
      csvBody.add(row);
    }

    return csvBody;
  }

  private List<String> generateDynamicHeaders(
      FeeDto.FeeDueReportRequest request, List<FeeHead> currentFeeHeads) {

    List<String> headers = new ArrayList<>(CSV_HEADERS);
    Map<String, Set<String>> feeGroupStructure = buildFeeGroupStructure(request, currentFeeHeads);

    for (Map.Entry<String, Set<String>> entry : feeGroupStructure.entrySet()) {
      String feeGroupDescription = entry.getKey();
      Set<String> feeTypes = entry.getValue();

      if (feeTypes.isEmpty()) {
        headers.add(feeGroupDescription);
      } else {
        for (String feeType : feeTypes) {
          headers.add(feeGroupDescription + " " + feeType);
        }
      }
    }

    headers.add("DISCOUNT AMOUNT");
    headers.add("TOTAL DUE AMOUNT");
    //    headers.addAll(List.of("Delete", "EXTENDED DAY", "Fee Remark"));

    return headers;
  }

  private Map<String, Set<String>> buildFeeGroupStructure(
      FeeDto.FeeDueReportRequest request, List<FeeHead> currentFeeHeads) {
    Map<String, Set<String>> feeGroupStructure = new LinkedHashMap<>();

    if (currentFeeHeads != null) {
      for (FeeHead feeHead : currentFeeHeads) {
        if (feeHead.getFeeMaster() != null
            && feeHead.getFeeMaster().getFeeGroup() != null
            && feeHead.getFeeType() != null) {
          String feeGroupDescription = feeHead.getFeeMaster().getFeeGroup().getName();
          String feeTypeName = feeHead.getFeeType().getName();

          feeGroupStructure
              .computeIfAbsent(feeGroupDescription, k -> new LinkedHashSet<>())
              .add(feeTypeName);
        }
      }
    }

    List<String> requestedFeeGroupTypes = request.feeGroupTypes();
    if (requestedFeeGroupTypes != null && !requestedFeeGroupTypes.isEmpty()) {
      Map<String, Set<String>> filteredStructure = new LinkedHashMap<>();
      for (String requestedType : requestedFeeGroupTypes) {
        if (feeGroupStructure.containsKey(requestedType)) {
          filteredStructure.put(requestedType, feeGroupStructure.get(requestedType));
        }
      }
      return filteredStructure;
    }

    return feeGroupStructure;
  }

  private String getFeeGroupDescriptionForStudent(
      Long studentId, String feeTypeName, List<FeeHead> currentFeeHeads) {
    if (currentFeeHeads == null || studentId == null || feeTypeName == null) {
      return "Unknown Fee Group";
    }

    for (FeeHead feeHead : currentFeeHeads) {
      if (feeHead.getStudent() != null
          && feeHead.getStudent().getId() == (studentId)
          && feeHead.getFeeType() != null
          && feeTypeName.equals(feeHead.getFeeType().getName())
          && feeHead.getFeeMaster() != null
          && feeHead.getFeeMaster().getFeeGroup() != null) {
        return feeHead.getFeeMaster().getFeeGroup().getName();
      }
    }
    return "Unknown Fee Group";
  }

  private List<String> buildDynamicDataRow(
      FeeDto.FeeDueReportResponse report,
      FeeDto.FeeDueReportRequest request,
      List<FeeHead> currentFeeHeads) {
    List<String> row = new ArrayList<>();

    row.add(report.studentName() != null ? report.studentName() : "");
    row.add(report.fatherName() != null ? report.fatherName() : "");
    row.add(report.motherName() != null ? report.motherName() : "");
    row.add(report.guardianName() != null ? report.guardianName() : "");
    row.add(report.mobileNumber() != null ? report.mobileNumber() : "");
    row.add(report.admissionNumber() != null ? report.admissionNumber() : "");
    row.add(report.rollNumber() != null ? report.rollNumber() : "");
    row.add(report.sectionName() != null ? report.sectionName() : "");
    row.add(report.dateOfAdmission() != null ? report.dateOfAdmission() : "");

    Map<String, Map<String, Double>> feeGroupAmounts =
        buildFeeGroupAmountsMap(report, currentFeeHeads);
    Map<String, Set<String>> feeGroupStructure = buildFeeGroupStructure(request, currentFeeHeads);

    for (Map.Entry<String, Set<String>> entry : feeGroupStructure.entrySet()) {
      String feeGroupDescription = entry.getKey();
      Set<String> feeTypes = entry.getValue();

      if (feeTypes.isEmpty()) {
        Double amount =
            feeGroupAmounts.getOrDefault(feeGroupDescription, new HashMap<>()).values().stream()
                .mapToDouble(Double::doubleValue)
                .sum();
        row.add(String.format("%.0f", amount));
      } else {
        for (String feeType : feeTypes) {
          Double amount =
              feeGroupAmounts
                  .getOrDefault(feeGroupDescription, new HashMap<>())
                  .getOrDefault(feeType, 0.0);
          row.add(String.format("%.0f", amount));
        }
      }
    }

    row.add(String.format("%.0f", report.discountAmount()));
    row.add(String.format("%.0f", report.totalDueAmount()));
    //    row.add("0");
    //    row.add("0");
    //    row.add("");

    return row;
  }

  private Map<String, Map<String, Double>> buildFeeGroupAmountsMap(
      FeeDto.FeeDueReportResponse report, List<FeeHead> currentFeeHeads) {
    Map<String, Map<String, Double>> feeGroupAmounts = new HashMap<>();

    Long studentId = getStudentIdFromReport(report, currentFeeHeads);

    for (FeeDto.FeeDetailResponse detail : report.feeDetails()) {
      String feeGroupDescription =
          getFeeGroupDescriptionForStudent(studentId, detail.feeTypeName(), currentFeeHeads);
      String feeTypeName = detail.feeTypeName();
      Double amount = detail.balanceAmount() != null ? detail.balanceAmount() : 0.0;

      feeGroupAmounts
          .computeIfAbsent(feeGroupDescription, k -> new HashMap<>())
          .merge(feeTypeName, amount, Double::sum);
    }

    return feeGroupAmounts;
  }

  private Long getStudentIdFromReport(
      FeeDto.FeeDueReportResponse report, List<FeeHead> currentFeeHeads) {
    String admissionNumber = report.admissionNumber();
    if (admissionNumber == null) {
      return null;
    }

    for (FeeHead feeHead : currentFeeHeads) {
      if (feeHead.getStudent() != null
          && feeHead.getStudent().getUserInfo() != null
          && admissionNumber.equals(feeHead.getStudent().getUserInfo().getUserName())) {
        return feeHead.getStudent().getId();
      }
    }
    return null;
  }
  public void generateFeeHeadMasterAndStudentTermWiseReportCsv(
          String orgSlug, FeeDto.FeeReportRequest request, HttpServletResponse response) {

    String reportType = request.reportType();
    if ("fee_head_master".equals(reportType)) {
      generateFeeHeadMasterReportCsv(orgSlug, request, response);
    } else if ("student_term_wise".equals(reportType)) {
      generateStudentTermWiseReportCsv(orgSlug, request, response);
    } else {
      throw new IllegalArgumentException("Invalid report type: " + reportType);
    }
  }

  private void generateFeeHeadMasterReportCsv(
          String orgSlug, FeeDto.FeeReportRequest request, HttpServletResponse response) {

    List<Object[]> rawData = feeHeadRepository.findFeeHeadMasterReportData(orgSlug, request.academicYear(), request.sectionUuids());

    List<FeeDto.FeeReportResponse> reportData = processFeeHeadMasterData(rawData);
    List<String> dynamicHeaders = generateFeeHeadMasterHeaders(reportData);
    List<List<String>> csvData = buildFeeHeadMasterCsvData(reportData, dynamicHeaders);

    generateCsvResponse(csvData, dynamicHeaders, "Fee_Head_Master_Report"+ DateTimeFormatter.ofPattern("yyyy-MM-dd_HH:mm:ss").format(LocalDateTime.now())+".csv", response);
  }

  private void generateStudentTermWiseReportCsv(
          String orgSlug, FeeDto.FeeReportRequest request, HttpServletResponse response) {

    List<Object[]> rawData = feeHeadRepository.findStudentTermWiseReportData(
            orgSlug, request.academicYear(), request.term(),
            request.studentFilter());

    List<FeeDto.FeeReportResponse> reportData = processStudentTermWiseData(rawData);

    List<List<String>> csvData = buildStudentTermWiseCsvData(reportData);

    generateCsvResponse(csvData, STUDENT_TERM_WISE_HEADERS, "Student_Term_Wise_Report"+  DateTimeFormatter.ofPattern("yyyy-MM-dd_HH:mm:ss").format(LocalDateTime.now())+".csv", response);
  }

  private List<FeeDto.FeeReportResponse> processFeeHeadMasterData(List<Object[]> rawData) {
    Map<Long, FeeDto.FeeReportResponse.FeeReportResponseBuilder> studentDataMap = new HashMap<>();
    Map<Long, Map<String, Double>> feeApplicableMap = new HashMap<>();
    Map<Long, Map<String, FeeDto.FeeCollectionDetail>> feeCollectedMap = new HashMap<>();

    int sno = 1;
    for (Object[] row : rawData) {
      Long studentId = ((Number) row[0]).longValue();

      if (!studentDataMap.containsKey(studentId)) {
        FeeDto.FeeReportResponse.FeeReportResponseBuilder builder = FeeDto.FeeReportResponse.builder()
                .sno(sno++)
                .admissionNumber((String) row[1])
                .studentName((String) row[2])
                .fatherName((String) row[3])
                .fatherMobile((String) row[4])
                .motherName((String) row[5])
                .motherMobile((String) row[6])
                .rollNumber((String) row[7])
                .className((String) row[9])
                .gender((String) row[10])
                .studentCategory((String) row[11])
                .dateOfAdmission(formatDate(row[12]))
                .studentStatus((String) row[13]);

        studentDataMap.put(studentId, builder);
        feeApplicableMap.put(studentId, new HashMap<>());
        feeCollectedMap.put(studentId, new HashMap<>());
      }

      if (row[15] != null) {
        String feeTypeName = (String) row[15];
        Double feeAmount = row[16] != null ? ((Number) row[16]).doubleValue() : 0.0;
        Double paidAmount = row[17] != null ? ((Number) row[17]).doubleValue() : 0.0;

        feeApplicableMap.get(studentId).put(feeTypeName, feeAmount);

        if (paidAmount > 0) {
          String paymentDate = formatDate(row[18]);
          String receiptNumber = (String) row[19];

          feeCollectedMap.get(studentId).put(feeTypeName,
                  FeeDto.FeeCollectionDetail.builder()
                          .amount(paidAmount)
                          .date(paymentDate)
                          .receiptNumber(receiptNumber)
                          .build());
        }
      }
    }

    return studentDataMap.entrySet().stream()
            .map(entry -> {
              Long studentId = entry.getKey();
              FeeDto.FeeReportResponse.FeeReportResponseBuilder builder = entry.getValue();

              Map<String, Double> feeApplicable = feeApplicableMap.get(studentId);
              Map<String, FeeDto.FeeCollectionDetail> feeCollected = feeCollectedMap.get(studentId);

              Double grandTotal = feeApplicable.values().stream()
                      .mapToDouble(Double::doubleValue).sum();
              Double totalCollected = feeCollected.values().stream()
                      .mapToDouble(FeeDto.FeeCollectionDetail::amount).sum();
              Double dueAmount = grandTotal - totalCollected;

              return builder
                      .feeApplicable(feeApplicable)
                      .feeCollected(feeCollected)
                      .grandTotal(grandTotal)
                      .totalCollected(totalCollected)
                      .dueAmount(dueAmount)
                      .discounts(0.0)
                      .build();
            })
            .collect(Collectors.toList());
  }

  private List<FeeDto.FeeReportResponse> processStudentTermWiseData(List<Object[]> rawData) {
    return rawData.stream()
            .map(row -> FeeDto.FeeReportResponse.builder()
                    .studentName((String) row[0])
                    .admissionNumber((String) row[1])
                    .className((String) row[2])
                    .fatherName((String) row[3])
                    .rollNumber((String) row[4])
                    .studentStatus((String) row[5])
                    .feeHead((String) row[6])
                    .term((String) row[7])
                    .amount(row[8] != null ? ((Number) row[8]).doubleValue() : 0.0)
                    .dueDate(formatDate(row[9]))
                    .build())
            .collect(Collectors.toList());
  }

  private List<String> generateFeeHeadMasterHeaders(List<FeeDto.FeeReportResponse> reportData) {
    List<String> headers = new ArrayList<>(FEE_HEAD_MASTER_HEADERS);

    Set<String> feeTypes = reportData.stream()
            .flatMap(response -> response.feeApplicable().keySet().stream())
            .collect(Collectors.toSet());

    headers.addAll(feeTypes);

    headers.add("Total");
    headers.add("Discounts");
    headers.add("Grand Total");

    feeTypes.forEach(feeType -> headers.add(feeType + " (Collected)"));

    headers.add("Total Collected");
    headers.add("Due");

    return headers;
  }

  private String formatAmount(Double amount) {
    return amount != null ? String.format("%.2f", amount) : "0.0";
  }

  private List<List<String>> buildFeeHeadMasterCsvData(
          List<FeeDto.FeeReportResponse> reportData, List<String> headers) {

    List<List<String>> csvData = new ArrayList<>();

    for (FeeDto.FeeReportResponse response : reportData) {
      List<String> row = new ArrayList<>();

      row.add(String.valueOf(response.sno()));
      row.add(response.admissionNumber());
      row.add(response.studentName());
      row.add(response.fatherName());
      row.add(response.fatherMobile());
      row.add(response.motherName());
      row.add(response.rollNumber());
      row.add(response.motherMobile());
      row.add(response.className());
      row.add(response.gender());
      row.add(response.studentCategory());
      row.add(response.dateOfAdmission());
      row.add(response.studentStatus());

      Set<String> feeTypes = extractFeeTypesFromHeaders(headers);
      for (String feeType : feeTypes) {
        Double amount = response.feeApplicable().getOrDefault(feeType, 0.0);
        row.add(formatAmount(amount));
      }

      row.add(formatAmount(response.grandTotal()));
      row.add(formatAmount(response.discounts()));
      row.add(formatAmount(response.grandTotal() - response.discounts()));

      for (String feeType : feeTypes) {
        FeeDto.FeeCollectionDetail detail = response.feeCollected().get(feeType);
        row.add(detail != null ? formatAmount(detail.amount()) : "0.0");
      }

      row.add(formatAmount(response.totalCollected()));
      row.add(formatAmount(response.dueAmount()));

      csvData.add(row);
    }

    return csvData;
  }

  private List<List<String>> buildStudentTermWiseCsvData(List<FeeDto.FeeReportResponse> reportData) {
    return reportData.stream()
            .map(response -> Arrays.asList(
                    response.studentName(),
                    response.admissionNumber(),
                    response.className(),
                    response.fatherName(),
                    response.rollNumber(),
                    response.studentStatus(),
                    response.feeHead(),
                    response.term(),
                    formatAmount(response.amount()),
                    response.dueDate()
            ))
            .collect(Collectors.toList());
  }

  private Set<String> extractFeeTypesFromHeaders(List<String> headers) {
    Set<String> feeTypes = new LinkedHashSet<>();
    boolean inFeeSection = false;

    for (String header : headers) {
      if (header.equals("Student Status")) {
        inFeeSection = true;
        continue;
      }
      if (header.equals("Total")) {
        break;
      }
      if (inFeeSection) {
        feeTypes.add(header);
      }
    }

    return feeTypes;
  }

  private void generateCsvResponse(
          List<List<String>> csvData, List<String> headers, String fileName, HttpServletResponse response) {

    response.setContentType("text/csv; charset=UTF-8");
    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

    if (csvData.isEmpty()) {
      CsvUtils.generateCsv(headers.toArray(new String[0]), new ArrayList<>(), response);
      return;
    }

    CsvUtils.generateCsv(headers.toArray(new String[0]), csvData, response);
  }

  private String formatDate(Object dateObj) {
    if (dateObj == null) return "";
    if (dateObj instanceof LocalDateTime) {
      return ((LocalDateTime) dateObj).format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));
    }
    return dateObj.toString();
  }

  // New methods for fee_head_master and student_term_wise reports
  private void generateNewReportTypeCsv(
      String orgSlug, FeeDto.FeeDueReportRequest request, HttpServletResponse response, String reportType) {

    if ("fee_head_master".equals(reportType)) {
      generateFeeHeadMasterReportCsv(orgSlug, request, response);
    } else if ("student_term_wise".equals(reportType)) {
      generateStudentTermWiseReportCsv(orgSlug, request, response);
    }
  }

  private void generateFeeHeadMasterReportCsv(
      String orgSlug, FeeDto.FeeDueReportRequest request, HttpServletResponse response) {

    // Use existing queries to get fee heads data
    List<Student> students = getStudentsBySectionUuids(request);
    List<FeeHead> feeHeads = getFeeHeadsByReportType(orgSlug, students, request);

    // Process data to group by student and fee types
    Map<Long, StudentFeeData> studentDataMap = processFeeHeadMasterData(students, feeHeads);

    // Generate two-row headers based on fee types found in data
    HeaderStructure headerStructure = generateFeeHeadMasterHeaders(feeHeads);

    // Build CSV data with two-row headers
    List<List<String>> csvData = buildFeeHeadMasterCsvData(studentDataMap, headerStructure);

    // Generate CSV response with two-row headers
    generateTwoRowHeaderCsvResponse(csvData, headerStructure, "Fee_Head_Master_Report.csv", response);
  }

  private void generateStudentTermWiseReportCsv(
      String orgSlug, FeeDto.FeeDueReportRequest request, HttpServletResponse response) {

    // Use existing queries to get fee heads data
    List<Student> students = getStudentsBySectionUuids(request);
    List<FeeHead> feeHeads = getFeeHeadsByReportType(orgSlug, students, request);

    // Process data for term-wise report
    List<StudentTermData> termData = processStudentTermWiseData(students, feeHeads);

    // Build CSV data
    List<List<String>> csvData = buildStudentTermWiseCsvData(termData);

    // Generate CSV response with single header
    generateSingleHeaderCsvResponse(csvData, STUDENT_TERM_WISE_HEADERS, "Student_Term_Wise_Report.csv", response);
  }

  private Map<Long, StudentFeeData> processFeeHeadMasterData(List<Student> students, List<FeeHead> feeHeads) {
    Map<Long, StudentFeeData> studentDataMap = new HashMap<>();
    Map<Long, List<FeeHead>> feeHeadsByStudent =
        feeHeads.stream().collect(Collectors.groupingBy(fh -> fh.getStudent().getId()));

    int sno = 1;
    for (Student student : students) {
      StudentFeeData studentData = new StudentFeeData();
      studentData.sno = sno++;
      studentData.admissionNumber = student.getUserInfo().getUserName();
      studentData.studentName = student.getUserInfo().getFirstName() + " " +
          (student.getUserInfo().getLastName() != null ? student.getUserInfo().getLastName() : "");

      // Get guardian information
      var guardians = guardianService.getGuardiansByStudentId(student.getId());
      var father = guardians.stream().filter(g -> g.getRole() == GuardianRole.FATHER).findFirst();
      var mother = guardians.stream().filter(g -> g.getRole() == GuardianRole.MOTHER).findFirst();

      studentData.fatherName = father.map(g -> g.getFirstName()).orElse("");
      studentData.fatherMobile = father.map(g -> g.getMobileNumber()).orElse("");
      studentData.motherName = mother.map(g -> g.getFirstName()).orElse("");
      studentData.motherMobile = mother.map(g -> g.getMobileNumber()).orElse("");

      studentData.rollNumber = student.getRollNumber();
      studentData.classRollNumber = student.getClassRollNumber();
      studentData.className = student.getSection() != null ? student.getSection().getName() : "";
      studentData.gender = student.getUserInfo().getGender() != null ? student.getUserInfo().getGender().toString() : "";
      studentData.studentCategory = "DAY SCHOLAR";
      studentData.dateOfAdmission = formatDate(student.getCreatedAt());
      studentData.studentStatus = student.getActive() == '1' ? "ACTIVE" : "INACTIVE";

      studentData.feeApplicable = new HashMap<>();
      studentData.feeCollected = new HashMap<>();

      // Process fee heads for this student
      List<FeeHead> studentFeeHeads = feeHeadsByStudent.getOrDefault(student.getId(), new ArrayList<>());
      for (FeeHead feeHead : studentFeeHeads) {
        String feeTypeName = feeHead.getFeeType().getName();
        Double feeAmount = feeHead.getAmount() != null ? feeHead.getAmount() : 0.0;
        Double paidAmount = feeHead.getPaidAmount() != null ? feeHead.getPaidAmount() : 0.0;

        studentData.feeApplicable.put(feeTypeName, feeAmount);

        if (paidAmount > 0) {
          if (!studentData.feeCollected.containsKey(feeTypeName)) {
            studentData.feeCollected.put(feeTypeName, new ArrayList<>());
          }

          PaymentDetail payment = new PaymentDetail();
          payment.date = formatDate(feeHead.getCreatedAt());
          payment.receiptNumber = ""; // TODO: Get actual receipt number from payment details
          payment.amount = paidAmount;

          studentData.feeCollected.get(feeTypeName).add(payment);
        }
      }

      // Calculate totals
      studentData.grandTotal = studentData.feeApplicable.values().stream()
          .mapToDouble(Double::doubleValue).sum();
      studentData.totalCollected = studentData.feeCollected.values().stream()
          .flatMap(List::stream)
          .mapToDouble(payment -> payment.amount)
          .sum();
      studentData.dueAmount = studentData.grandTotal - studentData.totalCollected;
      studentData.discounts = calculateDiscount(student);

      studentDataMap.put(student.getId(), studentData);
    }

    return studentDataMap;
  }

  private HeaderStructure generateFeeHeadMasterHeaders(List<FeeHead> feeHeads) {
    // Get all unique fee types and fee groups from the data
    Set<String> feeTypes = feeHeads.stream()
        .map(fh -> fh.getFeeType().getName())
        .collect(Collectors.toCollection(LinkedHashSet::new));

    // Get fee group descriptions from database
    Map<String, String> feeGroupDescriptions = feeHeads.stream()
        .collect(Collectors.toMap(
            fh -> fh.getFeeType().getName(),
            fh -> fh.getFeeMaster().getFeeGroup().getDescription() != null ?
                  fh.getFeeMaster().getFeeGroup().getDescription() :
                  fh.getFeeMaster().getFeeGroup().getName(),
            (existing, replacement) -> existing,
            LinkedHashMap::new
        ));

    HeaderStructure structure = new HeaderStructure();

    // Row 1: Main headers
    List<String> mainHeaders = new ArrayList<>(FEE_HEAD_MASTER_HEADERS);
    mainHeaders.add("FEE APPLICABLE");

    // Add fee group descriptions for fee type columns
    for (int i = 1; i < feeTypes.size(); i++) {
      mainHeaders.add(""); // Will be spanned by FEE APPLICABLE
    }

    mainHeaders.add("Total");
    mainHeaders.add("Discounts");
    mainHeaders.add("Grand Total");
    mainHeaders.add("FEE COLLECTED");

    // Add fee type main headers for collected section (each fee type has 3 sub-columns)
    for (String feeType : feeTypes) {
      mainHeaders.add(feeType);
      mainHeaders.add(""); // Date sub-column (spanned by fee type name)
      mainHeaders.add(""); // Receipt sub-column (spanned by fee type name)
    }
    mainHeaders.add("Total");
    mainHeaders.add("Due");

    // Row 2: Sub headers
    List<String> subHeaders = new ArrayList<>();
    // Basic student info - use actual column names
    subHeaders.addAll(FEE_HEAD_MASTER_HEADERS);

    // Fee applicable sub headers - use actual fee type names
    for (String feeType : feeTypes) {
      subHeaders.add(feeType);
    }
    subHeaders.add("Total");
    subHeaders.add("Discounts");
    subHeaders.add("Grand Total");

    // Fee collected sub headers - use meaningful column names
    for (String feeType : feeTypes) {
      subHeaders.add("Date");
      subHeaders.add("Receipt No");
      subHeaders.add("Amount");
    }
    subHeaders.add("Total");
    subHeaders.add("Due");

    structure.mainHeaders = mainHeaders;
    structure.subHeaders = subHeaders;
    structure.feeTypes = new ArrayList<>(feeTypes);
    structure.feeGroupDescriptions = feeGroupDescriptions;

    return structure;
  }

  // Helper classes for data structure
  private static class StudentFeeData {
    int sno;
    String admissionNumber;
    String studentName;
    String fatherName;
    String fatherMobile;
    String motherName;
    String motherMobile;
    String rollNumber;
    String classRollNumber;
    String className;
    String gender;
    String studentCategory;
    String dateOfAdmission;
    String studentStatus;
    Map<String, Double> feeApplicable;
    Map<String, List<PaymentDetail>> feeCollected;
    Double grandTotal;
    Double totalCollected;
    Double dueAmount;
    Double discounts;
  }

  private static class PaymentDetail {
    String date;
    String receiptNumber;
    Double amount;
  }

  private static class HeaderStructure {
    List<String> mainHeaders;
    List<String> subHeaders;
    List<String> feeTypes;
    Map<String, String> feeGroupDescriptions;
  }

  private static class StudentTermData {
    String studentName;
    String registrationId;
    String className;
    String fatherName;
    String classRollNumber;
    String studentStatus;
    String feeHead;
    String term;
    Double amount;
    String dueDate;
  }

  private List<StudentTermData> processStudentTermWiseData(List<Student> students, List<FeeHead> feeHeads) {
    List<StudentTermData> termDataList = new ArrayList<>();
    Map<Long, List<FeeHead>> feeHeadsByStudent =
        feeHeads.stream().collect(Collectors.groupingBy(fh -> fh.getStudent().getId()));

    for (Student student : students) {
      List<FeeHead> studentFeeHeads = feeHeadsByStudent.getOrDefault(student.getId(), new ArrayList<>());

      // Get guardian information
      var guardians = guardianService.getGuardiansByStudentId(student.getId());
      var father = guardians.stream().filter(g -> g.getRole() == GuardianRole.FATHER).findFirst();

      for (FeeHead feeHead : studentFeeHeads) {
        StudentTermData termData = new StudentTermData();
        termData.studentName = student.getUserInfo().getFirstName() + " " +
            (student.getUserInfo().getLastName() != null ? student.getUserInfo().getLastName() : "");
        termData.registrationId = student.getUserInfo().getUserName();
        termData.className = student.getSection() != null ? student.getSection().getName() : "";
        termData.fatherName = father.map(g -> g.getFirstName()).orElse("");
        termData.classRollNumber = student.getClassRollNumber();
        termData.studentStatus = student.getActive() == '1' ? "active" : "inactive";
        termData.feeHead = feeHead.getFeeType().getName();
        termData.term = "Term 1"; // TODO: Determine actual term from fee head
        termData.amount = feeHead.getAmount() != null ? feeHead.getAmount() : 0.0;
        termData.dueDate = formatDate(feeHead.getDueDate());

        termDataList.add(termData);
      }
    }

    return termDataList;
  }

  private List<List<String>> buildFeeHeadMasterCsvData(
      Map<Long, StudentFeeData> studentDataMap, HeaderStructure headerStructure) {

    List<List<String>> csvData = new ArrayList<>();

    for (StudentFeeData studentData : studentDataMap.values()) {
      List<String> row = new ArrayList<>();

      // Basic student info
      row.add(String.valueOf(studentData.sno));
      row.add(studentData.admissionNumber != null ? studentData.admissionNumber : "");
      row.add(studentData.studentName != null ? studentData.studentName : "");
      row.add(studentData.fatherName != null ? studentData.fatherName : "");
      row.add(studentData.fatherMobile != null ? studentData.fatherMobile : "");
      row.add(studentData.motherName != null ? studentData.motherName : "");
      row.add(studentData.rollNumber != null ? studentData.rollNumber : "");
      row.add(studentData.motherMobile != null ? studentData.motherMobile : "");
      row.add(studentData.className != null ? studentData.className : "");
      row.add(studentData.gender != null ? studentData.gender : "");
      row.add(studentData.studentCategory != null ? studentData.studentCategory : "");
      row.add(studentData.dateOfAdmission != null ? studentData.dateOfAdmission : "");
      row.add(studentData.studentStatus != null ? studentData.studentStatus : "");

      // Fee applicable amounts
      for (String feeType : headerStructure.feeTypes) {
        Double amount = studentData.feeApplicable.getOrDefault(feeType, 0.0);
        row.add(formatAmount(amount));
      }

      row.add(formatAmount(studentData.grandTotal));
      row.add(formatAmount(studentData.discounts));
      row.add(formatAmount(studentData.grandTotal - studentData.discounts));

      // Fee collected amounts (Date, Receipt No, Amount for each fee type)
      for (String feeType : headerStructure.feeTypes) {
        List<PaymentDetail> payments = studentData.feeCollected.getOrDefault(feeType, new ArrayList<>());
        if (!payments.isEmpty()) {
          PaymentDetail firstPayment = payments.get(0);
          row.add(firstPayment.date);
          row.add(firstPayment.receiptNumber);
          row.add(formatAmount(firstPayment.amount));
        } else {
          row.add(""); // Date
          row.add(""); // Receipt No
          row.add("0"); // Amount
        }
      }

      row.add(formatAmount(studentData.totalCollected));
      row.add(formatAmount(studentData.dueAmount));

      csvData.add(row);
    }

    return csvData;
  }

  private List<List<String>> buildStudentTermWiseCsvData(List<StudentTermData> termDataList) {
    return termDataList.stream()
        .map(termData -> Arrays.asList(
            termData.studentName != null ? termData.studentName : "",
            termData.registrationId != null ? termData.registrationId : "",
            termData.className != null ? termData.className : "",
            termData.fatherName != null ? termData.fatherName : "",
            termData.classRollNumber != null ? termData.classRollNumber : "",
            termData.studentStatus != null ? termData.studentStatus : "",
            termData.feeHead != null ? termData.feeHead : "",
            termData.term != null ? termData.term : "",
            formatAmount(termData.amount),
            termData.dueDate != null ? termData.dueDate : ""
        ))
        .collect(Collectors.toList());
  }

  private void generateTwoRowHeaderCsvResponse(
      List<List<String>> csvData, HeaderStructure headerStructure, String fileName, HttpServletResponse response) {

    response.setContentType("text/csv; charset=UTF-8");
    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

    try {
      var writer = response.getWriter();

      // Write main headers (Row 1)
      writer.println(String.join(",", headerStructure.mainHeaders.stream()
          .map(this::escapeCsvValue)
          .collect(Collectors.toList())));

      // Write sub headers (Row 2)
      writer.println(String.join(",", headerStructure.subHeaders.stream()
          .map(this::escapeCsvValue)
          .collect(Collectors.toList())));

      // Write data rows
      for (List<String> row : csvData) {
        writer.println(String.join(",", row.stream()
            .map(this::escapeCsvValue)
            .collect(Collectors.toList())));
      }

      writer.flush();
    } catch (Exception e) {
      log.error("Error generating CSV response", e);
      throw new RuntimeException("Error generating CSV response", e);
    }
  }

  private void generateSingleHeaderCsvResponse(
      List<List<String>> csvData, List<String> headers, String fileName, HttpServletResponse response) {

    response.setContentType("text/csv; charset=UTF-8");
    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

    if (csvData.isEmpty()) {
      CsvUtils.generateCsv(headers.toArray(new String[0]), new ArrayList<>(), response);
      return;
    }

    CsvUtils.generateCsv(headers.toArray(new String[0]), csvData, response);
  }

  private String escapeCsvValue(String value) {
    if (value == null) return "";
    if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
      return "\"" + value.replace("\"", "\"\"") + "\"";
    }
    return value;
  }

}
