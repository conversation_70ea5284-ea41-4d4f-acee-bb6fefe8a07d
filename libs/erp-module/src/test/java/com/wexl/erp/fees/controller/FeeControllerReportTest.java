package com.wexl.erp.fees.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.service.FeeReportService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(FeeController.class)
class FeeControllerReportTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private FeeReportService feeReportService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testGenerateFeeHeadMasterReportCsv() throws Exception {
        // Given
        String orgSlug = "test-org";
        FeeDto.FeeReportRequest request = FeeDto.FeeReportRequest.builder()
                .academicYear("2024-25")
                .sectionUuids(Arrays.asList("section-uuid-1", "section-uuid-2"))
                .build();

        // When & Then
        mockMvc.perform(post("/orgs/{orgSlug}/fee-head-master-report/csv", orgSlug)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());

        verify(feeReportService).generateFeeReportCsv(eq(orgSlug), any(FeeDto.FeeReportRequest.class), any());
    }

    @Test
    void testGenerateStudentTermWiseReportCsv() throws Exception {
        // Given
        String orgSlug = "test-org";
        FeeDto.FeeReportRequest request = FeeDto.FeeReportRequest.builder()
                .academicYear("2024-25")
                .term("Term 1")
                .classFilter("STRAWBERRY-1")
                .studentFilter("MIRANSH")
                .build();

        // When & Then
        mockMvc.perform(post("/orgs/{orgSlug}/student-term-wise-report/csv", orgSlug)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());

        verify(feeReportService).generateFeeReportCsv(eq(orgSlug), any(FeeDto.FeeReportRequest.class), any());
    }

    @Test
    void testFeeHeadMasterReportWithMinimalRequest() throws Exception {
        // Given
        String orgSlug = "test-org";
        FeeDto.FeeReportRequest request = FeeDto.FeeReportRequest.builder()
                .academicYear("2024-25")
                .build();

        // When & Then
        mockMvc.perform(post("/orgs/{orgSlug}/fee-head-master-report/csv", orgSlug)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());

        verify(feeReportService).generateFeeReportCsv(eq(orgSlug), any(FeeDto.FeeReportRequest.class), any());
    }

    @Test
    void testStudentTermWiseReportWithMinimalRequest() throws Exception {
        // Given
        String orgSlug = "test-org";
        FeeDto.FeeReportRequest request = FeeDto.FeeReportRequest.builder()
                .academicYear("2024-25")
                .build();

        // When & Then
        mockMvc.perform(post("/orgs/{orgSlug}/student-term-wise-report/csv", orgSlug)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());

        verify(feeReportService).generateFeeReportCsv(eq(orgSlug), any(FeeDto.FeeReportRequest.class), any());
    }

    @Test
    void testInvalidOrgSlug() throws Exception {
        // Given
        String invalidOrgSlug = "";
        FeeDto.FeeReportRequest request = FeeDto.FeeReportRequest.builder()
                .academicYear("2024-25")
                .build();

        // When & Then
        mockMvc.perform(post("/orgs/{orgSlug}/fee-head-master-report/csv", invalidOrgSlug)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isNotFound());
    }

    @Test
    void testMissingRequestBody() throws Exception {
        // Given
        String orgSlug = "test-org";

        // When & Then
        mockMvc.perform(post("/orgs/{orgSlug}/fee-head-master-report/csv", orgSlug)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testInvalidJsonRequest() throws Exception {
        // Given
        String orgSlug = "test-org";
        String invalidJson = "{ invalid json }";

        // When & Then
        mockMvc.perform(post("/orgs/{orgSlug}/fee-head-master-report/csv", orgSlug)
                .contentType(MediaType.APPLICATION_JSON)
                .content(invalidJson))
                .andExpect(status().isBadRequest());
    }
}
